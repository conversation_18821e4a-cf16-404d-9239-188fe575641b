#include <dynamic_manage/dynamic_manage.h>

using namespace std;

/**
 * @说明	 构造函�?
 */
DynamicManage::DynamicManage()
{
	ros::NodeHandle private_nh("~");
	ros::NodeHandle nh;

	// 读取解析配置文件基础路径
	string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	
    // 读取解析配置文件
    try
    {
        log4cpp::PropertyConfigurator::configure(config_base_path+"/dynamic.conf");
    }
    catch (log4cpp::ConfigureFailure& f)
    {
        std::cout << "Configure Problem: " << f.what() << std::endl;
    }

	leg_pose_sub_ = private_nh.subscribe("/leg_tracker_measurements_filter", 100, &DynamicManage::leg_pose_call_back, this);

	robot_states_pub_ = nh.advertise<riki_msgs::robot_status>("robot_states", 1);

	nav_states_sub_ = nh.subscribe("nav_manage_node/robot_nav_status", 10, &DynamicManage::robot_nav_status_back, this);
	
	robot_pose_sub_ = nh.subscribe("/robot_pose", 5, &DynamicManage::robot_pose_call_back, this);
	
	robot_cmd_sub_ = nh.subscribe("/cmd_vel", 5, &DynamicManage::robot_cmd_vel_back, this);

    speed_areas_ser_= nh.advertiseService("/speed_area",&DynamicManage::speed_area_back,this);
	
	

	people_id_ = 10000;
	speed_level_ = NORMAL;
	speed_charge_flag_ = false;
	find_people_ = false;
	last_msg_id_ = 0;
	rotate_level_ = RLOW;
	log.info("DynamicManage");
}


/**
 * @说明	 析构函数
 */
DynamicManage::~DynamicManage()
{

}

bool DynamicManage::speed_area_back(riki_msgs::speedAreas::Request &req, riki_msgs::speedAreas::Response &res)
{
	int point_num = req.speedAreas.size();
	log.info("速度区域数量：%d",point_num);
	
	speed_areas.clear();
	max_line_speed.clear();
	max_angualr_speed.clear();
	
	if(point_num == 0)
	{
		res.ret=0;
		return true;
	}
	
	std::vector<geometry_msgs::Point> points;
	
	for(int i=0;i<point_num;i++)
	{
		for(int j=0;j<req.speedAreas[i].points.size();j++)
		{
			points.push_back(req.speedAreas[i].points[j]);
		}
		speed_areas.push_back(points);
		max_line_speed.push_back(req.speedAreas[i].max_line_speed);
		max_angualr_speed.push_back(req.speedAreas[i].max_angular_speed);
		
		
		log.info("max_line_speed：%f",req.speedAreas[i].max_line_speed);
		log.info("max_angualr_speed：%f",req.speedAreas[i].max_angular_speed);
		points.clear();
	}
	
	res.ret=0;
	return true;
}

void DynamicManage::speed_change(bool &speed_change, double &speed_line, double &speed_angular)
{
	//log.info("更新速度");
	speed_change = false;
	
	double wx,wy;
	
	wx = robot_pose_.pose.position.x;
	wy = robot_pose_.pose.position.y;
	
	int i=-1;
	
    if(pointInPolygon(speed_areas, wx, wy, i))
	{
		
		speed_line = max_line_speed[i];
		speed_angular = max_angualr_speed[i];
		speed_change = true;
        log.info("切换速度上下限");
	}
}


void DynamicManage::robot_pose_call_back(geometry_msgs::Pose msg)
{
	robot_pose_.pose.position=msg.position;
       //log.info("robot_pose");
}

void DynamicManage::leg_pose_call_back(people_msgs::PositionMeasurementArray msg)
{
	dist_tmp =1000;
	//std::cout<<"leg_pose_call_back"<<std::endl;
	
	if(msg.people.size()==0)
		find_people_ = false;
	else
		find_people_ = true;
	
	
	
	for(int i=0;i<msg.people.size();i++)
	{
		//ROS_INFO_STREAM(msg.people[i]);
		if(msg.people[i].pos.x < 1.7 && msg.people[i].pos.x>-0.3&&msg.people[i].pos.y<0.85&&msg.people[i].pos.y>-0.85)
		{
			if(msg.people[i].pos.x < 1.4 && msg.people[i].pos.x>-0.3&&msg.people[i].pos.y<0.65&&msg.people[i].pos.y>-0.65)
			{
				if(msg.people[i].pos.x < 1.1 && msg.people[i].pos.x>-0.3&&msg.people[i].pos.y<0.55&&msg.people[i].pos.y>-0.55)
				{
					if(msg.people[i].pos.x < 0.8 && msg.people[i].pos.x>-0.3&&msg.people[i].pos.y<0.45&&msg.people[i].pos.y>-0.45)
					{
						if(msg.people[i].pos.x < 0.4 && msg.people[i].pos.x>-0.3&&msg.people[i].pos.y<0.35&&msg.people[i].pos.y>-0.35)
						{
							dist_people = 1;					
						}										
					}
					else
						{
						if(dist_people>2)
						{
							dist_people = 2;
							dist_tmp = dist_people;
						}
					}								
				}
				else
				{
					if(dist_people>3)
					{
						dist_people = 3;
						dist_tmp = dist_people;
					}
				}			
			}
			else
			{
				
				if(dist_tmp>4)
				{
					dist_people = 4;
					dist_tmp = dist_people;
				}
			}
			
			
		}
		else
		{
			if(dist_people<dist_tmp)
			{
				dist_people = 5;
				dist_tmp = dist_people;
			}
		}	
	}
	
	
}

void DynamicManage::update_speed_level(bool &speed_charge_flag, int &speed_level)
{
	int speed_level_tmp = speed_level_;

	if(find_people_ == true)	
	{
		//log.info("dist_people: %d",dist_people);
		switch(dist_people)
		{
			case 1:
				 speed_level_ = LOW_1;
				 break;
			case 2:
				 speed_level_ = LOW_2;
				 break;
			case 3:
				 speed_level_ = LOW_3;
				 break;
			case 4:
				 speed_level_ = LOW_4;
				 break;
			case 5:
				 speed_level_ = NORMAL;
				 break;	
			default:
				 speed_level_ = NORMAL;
				 break;		
		}
		dist_people =999;
		
		//log.info("发现人类！nav_states_ %d",nav_states_);
		if(nav_states_ == 405)
		{
			riki_msgs::robot_status msg;
			msg.statusCode = 105;
			robot_states_pub_.publish(msg);
		}else{
			riki_msgs::robot_status msg;
			msg.statusCode = 505;
			robot_states_pub_.publish(msg);


		}
		//log.info("发现人类");
		//ROS_INFO_STREAM(pose_base_);
	}
	else{

		
		riki_msgs::robot_status msg;
		msg.statusCode = 505;
		robot_states_pub_.publish(msg);
		//speed_level_ = NORMAL;
	}
	
	if(speed_level_ != speed_level_tmp)
	{
		//log.info("速度切换");
		speed_charge_flag_ = true;
	}else{
		speed_charge_flag_ = false;
	}	
	
	speed_charge_flag = speed_charge_flag_;
	speed_level = speed_level_;
}

void DynamicManage::robot_nav_status_back(const riki_msgs::robot_nav_status & msg)
{
	nav_states_ = msg.statusCode;
}

void DynamicManage::robot_cmd_vel_back(const geometry_msgs::Twist & msg)
{

	vel_x =abs(msg.linear.x);
	vel_z = abs(msg.angular.z);
}
void DynamicManage::update_rotate_level(bool &rotate_charge_flag, int &rotate_level)
{
	int rotate_level_tmp = rotate_level_;
	//log.info("---------------------------------------------- %f,  %f",vel_x,vel_z);
	if(vel_x < 0.02 && vel_z>0.01)
	{
		rotate_level_ = RFAST;
		//log.info("11111111111111111111111111111111%f",vel_x);
	}else{
		rotate_level_ = RLOW;
	}
	if(rotate_level_ != rotate_level_tmp)
	{
		log.info("");
		rotate_charge_flag_ = true;
	}else{
		rotate_charge_flag_ = false;
	}
	rotate_charge_flag = rotate_charge_flag_;
	rotate_level = rotate_level_;
}

bool DynamicManage::pointInPolygon(std::vector<std::vector<geometry_msgs::Point>> speed_areas_polygons, double x, double y, int& ii) {
	
	int res = 0;
	
	int num=0;
	std::vector<std::vector<geometry_msgs::Point>>::iterator it;
	for (it = speed_areas_polygons.begin(); it != speed_areas_polygons.end(); ++it)
	{
		
		std::vector<geometry_msgs::Point> polygon = *it;
		int poly_sides = polygon.size();
		
		//std::cout<<"poly_sides: "<<poly_sides<<std::endl;
		
		int i, j;
		j = poly_sides - 1;
		
		
		for (i = 0; i<poly_sides; i++)
		{
          //对每一条边进行遍历，该边的两个端点，有一个必须在待检测点(x,y)的左边，且两个点中，有一个点的y左边比p.y小，另一个点的y比p.y大。
         if ((polygon[i].y<y && polygon[j].y >= y || polygon[j].y<y && polygon[i].y >= y) && (polygon[i].x <= x || polygon[j].x <= x))
          {
             //用水平的直线与该边相交，求交点的x坐标。
             res ^= ((polygon[i].x + (y - polygon[i].y) / (polygon[j].y - polygon[i].y)*(polygon[j].x - polygon[i].x)) < x);
          }
          j = i;
		}
		
		log.info("res：%d",res);
		
		if(res!=0)
		{
			ii=num;
			return res;	
			
		}
		
		num++;
	}
	
	

    return res;	
}
