// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.optimization.proto;

import "cartographer/common/proto/ceres_solver_options.proto";

// NEXT ID: 26
message OptimizationProblemOptions {
  reserved 20 to 22; // For visual constraints.
  // Scaling parameter for Huber loss function.
  double huber_scale = 1;

  // Scaling parameter for the IMU acceleration term.
  double acceleration_weight = 8;

  // Scaling parameter for the IMU rotation term.
  double rotation_weight = 9;

  // Scaling parameter for translation between consecutive nodes based on the
  // local SLAM pose.
  double local_slam_pose_translation_weight = 14;

  // Scaling parameter for rotation between consecutive nodes based on the
  // local SLAM pose.
  double local_slam_pose_rotation_weight = 15;

  // Scaling parameter for translation between consecutive nodes based on the
  // odometry.
  double odometry_translation_weight = 16;

  // Scaling parameter for rotation between consecutive nodes based on the
  // odometry.
  double odometry_rotation_weight = 17;

  // Scaling parameter for the FixedFramePose translation. Unit: 1/meters.
  double fixed_frame_pose_translation_weight = 11;

  // Scaling parameter for the FixedFramePose rotation.
  double fixed_frame_pose_rotation_weight = 12;

  bool fixed_frame_pose_use_tolerant_loss = 23;
  // The following parameters are used only if fixed_frame_pose_use_tolerant_loss is true.
  // See http://ceres-solver.org/nnls_modeling.html.
  // For large values of s, the tolerant loss function approaches a null loss
  // with fixed_frame_pose_translation_weight.
  double fixed_frame_pose_tolerant_loss_param_a = 24;
  double fixed_frame_pose_tolerant_loss_param_b = 25;

  // 3D only: fix Z.
  bool fix_z_in_3d = 13;

  // 3D only: activate online IMU extrinsics.
  bool use_online_imu_extrinsics_in_3d = 18;

  // If true, the Ceres solver summary will be logged for every optimization.
  bool log_solver_summary = 5;

  common.proto.CeresSolverOptions ceres_solver_options = 7;
}
