cmake_minimum_required(VERSION 3.0.2)
project(charge_manage)

add_compile_options(-std=c++11)

find_package(catkin REQUIRED)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  riki_msgs
)

catkin_package()
include_directories(
  include 
  ${catkin_INCLUDE_DIRS}

)

add_library(charge_manage src/charge_manage.cpp)
add_executable(charge_manage_node src/charge_manage_node.cpp)
target_link_libraries(charge_manage_node charge_manage ${catkin_LIBRARIES} -pthread -llog4cpp)


install(TARGETS charge_manage_node charge_manage
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch
    DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)
