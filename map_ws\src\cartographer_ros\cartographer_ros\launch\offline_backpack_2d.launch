<!--
  Copyright 2018 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<launch>
  <arg name="bag_filenames"/>
  <arg name="no_rviz" default="false"/>
  <arg name="rviz_config" default="$(find cartographer_ros)/configuration_files/demo_2d.rviz"/>
  <arg name="configuration_directory" default="$(find cartographer_ros)/configuration_files"/>
  <arg name="configuration_basenames" default="backpack_2d.lua"/>
  <arg name="urdf_filenames" default="$(find cartographer_ros)/urdf/backpack_2d.urdf"/>
  <arg name="launch_prefix" default=""/>

  <remap from="echoes" to="horizontal_laser_2d"/>
  <include file="$(find cartographer_ros)/launch/offline_node.launch">
    <arg name="bag_filenames" value="$(arg bag_filenames)"/>
    <arg name="no_rviz" value="$(arg no_rviz)"/>
    <arg name="rviz_config" value="$(arg rviz_config)"/>
    <arg name="configuration_directory" value="$(arg configuration_directory)"/>
    <arg name="configuration_basenames" value="$(arg configuration_basenames)"/>
    <arg name="urdf_filenames" value="$(arg urdf_filenames)"/>
    <arg name="launch_prefix" value="$(arg launch_prefix)"/>
  </include>
</launch>
