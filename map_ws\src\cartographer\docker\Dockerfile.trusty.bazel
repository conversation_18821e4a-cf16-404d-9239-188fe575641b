# Copyright 2016 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM ubuntu:trusty

ARG cc
ARG cxx

# Set the preferred C/C++ compiler toolchain, if given (otherwise default).
ENV CC=$cc
ENV CXX=$cxx

COPY scripts/install_debs_bazel.sh cartographer/scripts/
RUN cartographer/scripts/install_debs_bazel.sh && rm -rf /var/lib/apt/lists/*
COPY . cartographer
RUN cartographer/scripts/install_cartographer_bazel.sh && rm -rf cartographer
