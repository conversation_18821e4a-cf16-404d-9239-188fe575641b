<launch>
<group ns="map_merge">
  <node pkg="multirobot_map_merge" type="map_merge" respawn="false" name="map_merge" output="screen">
    <param name="robot_map_topic" value="map"/>
    <param name="robot_namespace" value="/"/>
    <param name="merged_map_topic" value="map_merged"/>
    <param name="world_frame" value="world"/>
    <param name="known_init_poses" value="true"/>
    <param name="merging_rate" value="0.5"/>
    <param name="discovery_rate" value="0.05"/>
    <param name="estimation_rate" value="0.1"/>
    <param name="estimation_confidence" value="1.0"/>
    
    <remap from="/map_merge/map_merged" to="/map" />
    
  </node>
</group>
<!--group ns="/udrive0">
   <param name="map_merge/init_pose_x" value="0"/>
   <param name="map_merge/init_pose_y" value="0"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_y" value="0.0"/>
   <param name="map_merge/init_pose_orientation_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_w" value="1.0"/>
</group>

<group ns="/udrive1">
   <param name="map_merge/init_pose_x" value="9.2096"/>
   <param name="map_merge/init_pose_y" value="7.6165"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_y" value="0.0"/>
   <param name="map_merge/init_pose_orientation_z" value="1.0"/>
   <param name="map_merge/init_pose_orientation_w" value="0.0"/>
</group-->

<group ns="/udrive0">
   <param name="map_merge/init_pose_x" value="0"/>
   <param name="map_merge/init_pose_y" value="0"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_y" value="0.0"/>
   <param name="map_merge/init_pose_orientation_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_w" value="1.0"/>
</group>

<group ns="/udrive1">
   <param name="map_merge/init_pose_x" value="15.9117482294"/>
   <param name="map_merge/init_pose_y" value="-12.0269792589"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_orientation_x" value="0.0"/>
   <param name="map_merge/init_pose_orientation_y" value="0.0"/>
   <param name="map_merge/init_pose_orientation_z" value="-0.348633852789"/>
   <param name="map_merge/init_pose_orientation_w" value="0.937259001925"/>
</group>

<!--group ns="/udrive0">
   <param name="map_merge/init_pose_x" value="0"/>
   <param name="map_merge/init_pose_y" value="0"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_yaw" value="0.0"/>
</group>

<group ns="/udrive1">
   <param name="map_merge/init_pose_x" value="-11.9897589339"/>
   <param name="map_merge/init_pose_y" value="2.23570728782"/>
   <param name="map_merge/init_pose_z" value="0.0"/>
   <param name="map_merge/init_pose_yaw" value="0"/>
</group-->

</launch>