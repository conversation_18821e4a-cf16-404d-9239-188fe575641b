/**  
* doctor_mode.cpp
*
* 功 能： 实现消毒模式类
* 类 名： DoctorMode
*
* Ver	 变更日期		  负责人        变更内容
* ────────────────────────────────────────────────────────────────────
* V1.1.1  2020-10-23    N/A	     初版
*
* Copyright (c) 2020 udrive Corporation. All rights reserved.
*┌────────────────────────────────────────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．  │
*│　版权所有：南京驭行科技有限公司　　　　　　　	                   │
*└────────────────────────────────────────────────────────────────────┘
*/

/*
	修改计划：
		1 路径整体过滤的更符合消毒路径，包括最远点，转折点等点必须到达
		2 路径点必须能够满足机器人原地旋转无障碍
		3 增加跳过时间

*/

#include <doctor_mode/doctor_mode.h>

using namespace std;

/**
 * @说明	 构造函数
 */
DoctorMode::DoctorMode():ac("move_base", true)
{
	ros::NodeHandle private_nh("~");
	ros::NodeHandle nh;

	// 读取解析配置文件基础路径
	string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	
    // 读取解析配置文件
    try
    {
        log4cpp::PropertyConfigurator::configure(config_base_path+"/docotr_mode.conf");
    }
    catch (log4cpp::ConfigureFailure& f)
    {
        std::cout << "Configure Problem: " << f.what() << std::endl;
    }

	// 参数初始化
	private_nh.param("segment_length", segment_length, 2.0);
	private_nh.param("distance_threshold", distance_threshold, 1.5);
	private_nh.param("angle_threshold", angle_threshold, 0.5);
	private_nh.param("skip_wait_time", skip_wait_time, 60.0);

	//发布固定路线消毒模式服务
	doctor_mode = private_nh.advertiseService("doctor_mode", &DoctorMode::doctor_mode_call_back, this);

	//发布固定路线消毒状态
	doctor_mode_state_ = nh.advertise<const riki_msgs::doctor_mode_status>("doctor_mode_state", 10);

	//set up the doctor's thread
	doctor_thread_ = new boost::thread(boost::bind(&DoctorMode::doctorThread, this));
	
	nav_status_sub_ = nh.subscribe("nav_status_flag", 10, &DoctorMode::nav_status_flag_call_back, this);
	
	//可视化
	original_path_ = nh.advertise<const nav_msgs::Path>("original_path", 1);
	dp_path_ = nh.advertise<const nav_msgs::Path>("dp_path", 1);
	filter_path_ = nh.advertise<const nav_msgs::Path>("filter_path", 1);
	reached_path_ = nh.advertise<const nav_msgs::Path>("reached_path", 1);
	
	
	end_pose = false;
	time_out_flag = false;
	
	log.info("DoctorMode 构造完成");

}

/**
 * @说明	 析构函数
 */
DoctorMode::~DoctorMode()	
{
	doctor_thread_->interrupt();
    doctor_thread_->join();

	delete doctor_thread_;
}

double DoctorMode::perpendicularDistance(geometry_msgs::PoseStamped curPose, double A, double B, double C)
{
    double distance = (A * curPose.pose.position.x + B * curPose.pose.position.y + C) / sqrt(A * A + B * B);
    return distance < 0 ? -distance : distance;
}
 
// DouglasPeucker 算法
int DoctorMode::DouglasPeucker(std::vector<geometry_msgs::PoseStamped>& Points, int Start, int End, double epsilon,std::vector<int>& indexs)
{
    double dMax = 0;
    int index = 0;
    int iter;
 
    // 直线方程： Ax + By + C = 0
    double A, B, C;
    if(Points[Start].pose.position.x == Points[End].pose.position.x)
    {// 垂直x轴的直线
        A = 1;
        B = 0;
        C = -Points[Start].pose.position.x;
    }
    else
    {// 常规直线
        A = Points[End].pose.position.y - Points[Start].pose.position.y;     // y1 - y0
        B = -(Points[End].pose.position.x - Points[Start].pose.position.x);  // -(x1 - x0)
        C = Points[Start].pose.position.y * (-B)  - Points[Start].pose.position.x * A;
    }
    for(iter = Start + 1; iter <  End; ++iter)
    {
        // 点到直线的垂直距离
        double distance = perpendicularDistance(Points[iter], A, B, C);
        if(distance > dMax)
        {
            dMax = distance;
            index = iter;
        }
    }
	
    if(dMax > epsilon)
    {
        //cout << "dMax Distance Index = " << index << endl;
        indexs.push_back(index);
        DouglasPeucker(Points, Start, index, epsilon,indexs);
        DouglasPeucker(Points, index, End, epsilon,indexs);
    }
    
    return 0;
}

bool DoctorMode::doctor_mode_call_back(riki_msgs::yx_path_control::Request  &req,riki_msgs::yx_path_control::Response &res)
{
	int cmd = req.cmd;
	log.info("doctor_mode_call_back 接收到命令 cmd = %d!", cmd);

	geometry_msgs::PoseStamped pose_tmp;
	geometry_msgs::PoseStamped last_pose_tmp;
	
	switch(cmd){
		case START_FIX_ROUTE:
		{
			if(req.pose_info.size()==0)
			{
				res.ret = -1;
				return true;
			}

			//初始化
			mfixed_route_plan_.clear();
			reach_seq.clear();
			
			//路径可视化
			{
				nav_msgs::Path orginal_path_tmp_;
				orginal_path_tmp_.header.stamp = ros::Time::now();
				orginal_path_tmp_.header.frame_id = "map";
				geometry_msgs::PoseStamped orginal_path_pose_;
				for(int i=0;i<req.pose_info.size();i++)
				{			
					orginal_path_pose_.pose = req.pose_info[i];
					orginal_path_tmp_.poses.push_back(orginal_path_pose_);
				}
				original_path_.publish(orginal_path_tmp_);
				log.info("-------原始路径 size:%d-------",orginal_path_tmp_.poses.size());
			}
			
			/*
			std::vector<double> path_diff_angle_vec_;
			// 路径点角度差预计算
			for(int i=0;i<req.pose_info.size();i++)
			{	
				if(i<5 || i> req.pose_info.size() - 5 )
				{
					path_diff_angle_vec_.push_back(0);
				}
				else
				{
					std::vector<double> angle_vec_;
					for(int a=i-5;a<=i+5;a++)
					{
						double angle_tmp_ = atan2(req.pose_info[a+1].position.y-req.pose_info[a].position.y, req.pose_info[a+1].position.x-req.pose_info[a].position.x);
						angle_vec_.push_back(angle_tmp_);
						//std::cout << angle_tmp_ << std::endl;
					}
					
					
					std::cout << std::endl;
					std::vector<double> diff_angle_vec_;
					for(int n=0;n<angle_vec_.size();n++)
					{
						double diff;
						if(n!=5)
						{
							diff = fabs(angle_vec_[n]-angle_vec_[5]);
							diff_angle_vec_.push_back(diff);
						}
						std::cout << diff << std::endl;
					}

					std::vector<double>::iterator biggest = std::max_element(std::begin(diff_angle_vec_), std::end(diff_angle_vec_));
					double biggest_angle = normal_angle(*biggest);
					path_diff_angle_vec_.push_back(biggest_angle);
				}
			}
			*/
			
			/*
			for(int i=0;i<path_diff_angle_vec_.size();i++)
			{	
				std::cout << path_diff_angle_vec_[i] << " ";
			}
			std::cout << std::endl;
			*/
			
			double distance = 0;
			double last_distance = 0;
		   //读取路径  按距离取点
			for(int i=0;i<req.pose_info.size();i++)
			{			
				if(i == 0 || i == req.pose_info.size()-1)
				{
					pose_tmp.pose = req.pose_info[i];
					last_pose_tmp = pose_tmp;
					last_distance = 0;
					
					mfixed_route_plan_.push_back(pose_tmp);                 
					reach_seq.push_back(mfixed_route_plan_.size()-1);
					
				}
				else
				{
					distance = hypot(last_pose_tmp.pose.position.x-req.pose_info[i].position.x,
											last_pose_tmp.pose.position.y-req.pose_info[i].position.y);
					
					//log.info("相邻两点之间间距为：%f ,角度差为: %f",distance,path_diff_angle_vec_[i]);			
					if(last_distance > distance  )	
					{
						i--;
						log.info("last_distance：%f   distance：%f",last_distance,distance);
						pose_tmp.pose= req.pose_info[i];
						mfixed_route_plan_.push_back(pose_tmp);
						last_pose_tmp = pose_tmp;
						last_distance = 0;
						
					}else if(distance > segment_length)		
					{
						pose_tmp.pose= req.pose_info[i];
						mfixed_route_plan_.push_back(pose_tmp);
						
						last_pose_tmp = pose_tmp;
						last_distance = 0;

					}else
					{												
						last_distance = distance;
					}
					
				}
			}		
			
			
			// 过滤路径可视化
			nav_msgs::Path filter_path_tmp_;
			{
				
				filter_path_tmp_.header.stamp = ros::Time::now();
				filter_path_tmp_.header.frame_id = "map";
				geometry_msgs::PoseStamped filter_path_pose_;
				for(int n=0;n<mfixed_route_plan_.size();n++)
				{
					filter_path_pose_ = mfixed_route_plan_[n];
					filter_path_tmp_.poses.push_back(filter_path_pose_);
				}
				filter_path_.publish(filter_path_tmp_);
				log.info("-------过滤路径 size:%d-------",filter_path_tmp_.poses.size());
				
			}
			
			// DouglasPeucker算法优化
			{
				nav_msgs::Path dp_path_tmp_;
				dp_path_tmp_.header.stamp = ros::Time::now();
				dp_path_tmp_.header.frame_id = "map";
				geometry_msgs::PoseStamped dp_path_pose_;
				
				std::vector<geometry_msgs::PoseStamped> tmp_path_;
				
				for(int i=0;i<filter_path_tmp_.poses.size();i++)
				{
					pose_tmp = filter_path_tmp_.poses[i];
					tmp_path_ .push_back(pose_tmp);
				}

				DouglasPeucker(tmp_path_, 0, tmp_path_.size() - 1, 1.5,reach_seq);
				std::sort(reach_seq.begin(),reach_seq.end());
				
				for(int i = 0;i<reach_seq.size();i++)
				{
					std::cout << reach_seq[i] << std::endl;
					dp_path_pose_ = tmp_path_[reach_seq[i]];
					dp_path_tmp_.poses.push_back(dp_path_pose_);
				}
				
				dp_path_.publish(dp_path_tmp_);
				
				log.info("-------优化路径 size:%d-------",dp_path_tmp_.poses.size());
			}
			
			// 必达点可视化
			{
				nav_msgs::Path reached_path_tmp_;
				reached_path_tmp_.header.stamp = ros::Time::now();
				reached_path_tmp_.header.frame_id = "map";
				geometry_msgs::PoseStamped reached_path_pose_;
				std::vector<geometry_msgs::PoseStamped> tmp_path_;
				
				for(int n=0;n<reach_seq.size();n++)
				{
					reached_path_pose_ = mfixed_route_plan_[reach_seq[n]];
					reached_path_tmp_.poses.push_back(reached_path_pose_);
					
				}
				reached_path_.publish(reached_path_tmp_);
				
				log.info("-------必达点 size:%d-------",reached_path_tmp_.poses.size());
				
			}
			
			int plan_size = mfixed_route_plan_.size();
			log.info("固定路径一共 %d 点 ，处理后有 %d 点", req.pose_info.size(),plan_size);
			plan_size = reach_seq.size();
			log.info("路径上的必达点有 %d 个",plan_size);

			//唤醒线程
			runPlanner_ = true;
			doctor_cond_.notify_one();

			//发布消毒状态
			docotr_msgs.doctorState = START_FIX_ROUTE;
			docotr_msgs.doctorMode = 1;
			docotr_msgs.statusMsg = "doctor mode";
			doctor_mode_state_.publish(docotr_msgs);
			
			end_pose = false;
			
			res.ret = 1;
			log.info("开始消毒");
			last_valid_time = ros::Time::now();
			break;
		}	
		case PAUSE_FIX_ROUTE:
			//挂起线程
			runPlanner_ = false;
			sleep(1);
			ac.cancelGoal();

			//发布消毒状态
			docotr_msgs.doctorState = PAUSE_FIX_ROUTE;
			docotr_msgs.doctorMode = 1;
			docotr_msgs.statusMsg = "doctor mode";
			doctor_mode_state_.publish(docotr_msgs);

			res.ret = 1;
			log.info("暂停消毒");
			break;
		case CONTINUE_FIX_ROUTE:
			//唤醒线程
			runPlanner_ = true;
			doctor_cond_.notify_one();

			//发布消毒状态
			docotr_msgs.doctorState = CONTINUE_FIX_ROUTE;
			docotr_msgs.doctorMode = 1;
			docotr_msgs.statusMsg = "doctor mode";
			doctor_mode_state_.publish(docotr_msgs);

			res.ret = 1;
			log.info("继续消毒");
			last_valid_time = ros::Time::now();
			break;
		case STOP_FIX_ROUTE:
			//挂起线程
			
			runPlanner_ = false;
			
			mfixed_route_plan_.clear();

			//发布消毒状态
			docotr_msgs.doctorState = STOP_FIX_ROUTE;
			docotr_msgs.doctorMode = 1;
			docotr_msgs.statusMsg = "doctor mode";
			doctor_mode_state_.publish(docotr_msgs);

			res.ret = 1;
			log.info("结束消毒");
			break;
		default:
			log.debug("接收到未知指令:%d ，不执行任何消毒工作",cmd);
			res.ret = -1;
			break;
	}
	
	return true;
}

void DoctorMode::doneCb(const actionlib::SimpleClientGoalState& state,const move_base_msgs::MoveBaseResultConstPtr & result )
{	
	log.info("导航任务结束");
	last_valid_time = ros::Time::now();
	time_out_flag = false;

	//成功到达目标点
	if(state==actionlib::SimpleClientGoalState::SUCCEEDED)
	{
		log.info("到达目标点");		
	}
	//到达目标点失败
	else{
		log.info("未到达目标点，终点可能存在障碍物");
		//if(!end_pose)
		//{
		//	send_flag = true;
		//	log.info("跳到下个点");
		//}
		
	}

	if(end_pose)
	{
		//发布消毒状态
		docotr_msgs.doctorState = COMPLETE_FIX_ROUTE;
		docotr_msgs.doctorMode = 1;
		docotr_msgs.statusMsg = "doctor mode";
		doctor_mode_state_.publish(docotr_msgs);
		
		runPlanner_ = false;
		end_pose = false;

		reach_seq.clear();
		log.info("到达路径终点");
	}

	send_flag = true;
}

// 当目标激活的时候，会调用一次
void DoctorMode::ActiveCb() {
	log.info("发布目标");
}

// 接收服务器的反馈信息
void DoctorMode::FeedbackCb(const move_base_msgs::MoveBaseFeedbackConstPtr & feedback)
{
	log.info("机器人正在导航中");
}

void DoctorMode::nav_status_flag_call_back(std_msgs::Bool msg)
{
	skip_flag_ = msg.data;	
	log.info("接收到导航失败");
}

void DoctorMode::doctorThread(){
	log.debug("DoctorMode , Starting doctor thread...");
	ros::NodeHandle n;
	ros::Timer timer;
	bool wait_for_wake = false;
	boost::unique_lock<boost::recursive_mutex> lock(doctor_mutex_);
	move_base_msgs::MoveBaseGoal goal;
	geometry_msgs::Pose share_current_pose;
	
	double distance_;
	
	runPlanner_ = false;
	skip_flag_ = true;
	ros::Rate loop_rate(2);
	while(n.ok()){
		
		ros::spinOnce(); 
		while(wait_for_wake || !runPlanner_){
			log.debug("DoctorMode , doctor thread is suspending");
			doctor_cond_.wait(lock);
			wait_for_wake = false;
			send_flag = true;
			current_point_subscript = -1;
			time_out_flag = false;
		}
		
		{
				nav_msgs::Path filter_path_tmp_;
				filter_path_tmp_.header.stamp = ros::Time::now();
				filter_path_tmp_.header.frame_id = "map";
				geometry_msgs::PoseStamped filter_path_pose_;
				for(int n=0;n<mfixed_route_plan_.size();n++)
				{
					filter_path_pose_ = mfixed_route_plan_[n];
					filter_path_tmp_.poses.push_back(filter_path_pose_);
				}
				filter_path_.publish(filter_path_tmp_);
				log.info("-------过滤路径 size:%d-------",filter_path_tmp_.poses.size());
				
		}
		
		sharePose = ros::topic::waitForMessage<geometry_msgs::Pose>("robot_pose",  ros::Duration(4));
		if(sharePose != NULL)
		{	
			share_current_pose = *sharePose;
		}else{
			log.debug("获取不到机器人位置");
			runPlanner_ = true;
		}
		
		if(mfixed_route_plan_.size()>0 && send_flag)
		{
			if(mfixed_route_plan_.size() == 1)
			{
				end_pose = true;
			}

			goal.target_pose.header.frame_id = "map";
			goal.target_pose.header.stamp = ros::Time::now();
			goal.target_pose.pose = mfixed_route_plan_.front().pose;
			log.info("导航坐标为:[%f,%f]", goal.target_pose.pose.position.x,goal.target_pose.pose.position.y);
			goal.target_pose.pose.position.x +=  (double)random(0,10)/10000;
			goal.target_pose.pose.position.y +=  (double)random(0,10)/10000;
			log.info("导航坐标为:[%f,%f]", goal.target_pose.pose.position.x,goal.target_pose.pose.position.y);
			
			ac.sendGoal(goal,
					boost::bind(&DoctorMode::doneCb, this, _1, _2),
					boost::bind(&DoctorMode::ActiveCb, this),
		            boost::bind(&DoctorMode::FeedbackCb, this, _1));

			mfixed_route_plan_.pop_front();

			send_flag = false;
			
			current_point_subscript ++;

		}
		
		distance_ = hypot(share_current_pose.position.x-goal.target_pose.pose.position.x,
						  share_current_pose.position.y-goal.target_pose.pose.position.y);

		// 机器人位置接近下一点，并且下一点不是必达点	
		//							
		if((distance_< distance_threshold && std::find(reach_seq.begin(), reach_seq.end(), current_point_subscript) == reach_seq.end()))
		{
			log.info("接近当前目标，更改目标到下一点++++++++++++");
			send_flag = true;
			time_out_flag = false;
			last_valid_time = ros::Time::now();
			
		}else if((std::find(reach_seq.begin(), reach_seq.end(), current_point_subscript) != reach_seq.end() && distance_ < 0.15))
		{
			log.info("到达必达点附近，更改目标到下一点----------");
			send_flag = true;
			time_out_flag = false;
			last_valid_time = ros::Time::now();
		}
		
		
		if(!time_out_flag && last_valid_time + ros::Duration(skip_wait_time) < ros::Time::now())
		{
			log.info("-------超时，跳过当前点---------");
			send_flag = true;
			time_out_flag = true;
		}
		
		if(!skip_flag_)
		{
			log.info("当前点无法到达，更改目标到下一点");
			send_flag = true;
			skip_flag_ = true;
		}		
		
		loop_rate.sleep();	

		//log.info("DoctorMode Running ...");
		
		

	}	

}
