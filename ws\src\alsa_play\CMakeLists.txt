cmake_minimum_required(VERSION 3.0.2)
project(alsa_play)

add_compile_options(-std=c++11)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  roslib
  riki_msgs
)


catkin_package(
)

include_directories(
# include
  ${catkin_INCLUDE_DIRS}
)

add_executable(alsa_voice_node src/alsa_voice_node.cpp)

target_link_libraries(alsa_voice_node
   ${catkin_LIBRARIES} -pthread -lasound
 )

add_executable(alsa_node src/alsa_node.cpp)
target_link_libraries(alsa_node
   ${catkin_LIBRARIES} -pthread -llog4cpp)

 
add_executable(alsa_voice_background_node src/alsa_voice_background_node.cpp)
target_link_libraries(alsa_voice_background_node
   ${catkin_LIBRARIES} -pthread -lasound
 )

install(TARGETS alsa_voice_node alsa_node alsa_voice_background_node
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch
    DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
) 