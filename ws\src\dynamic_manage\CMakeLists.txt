cmake_minimum_required(VERSION 2.8.3)
project(dynamic_manage)

add_compile_options(-std=c++11)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  riki_msgs
  dynamic_reconfigure
  tf
)


catkin_package(
  INCLUDE_DIRS include
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

add_library(dynamic_manage src/dynamic_manage.cpp)
add_executable(dynamic_manage_node src/dynamic_manage_node.cpp)
target_link_libraries(dynamic_manage_node dynamic_manage ${catkin_LIBRARIES} -pthread -llog4cpp)

install(TARGETS dynamic_manage_node dynamic_manage
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

