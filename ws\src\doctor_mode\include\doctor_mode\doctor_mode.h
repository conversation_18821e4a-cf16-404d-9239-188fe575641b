/**  
* doctor_mode.h
*
* 功 能： 声明消毒模块单片机驱动类
* 类 名： DoctorMode
*
* Ver	  变更日期		   负责人       变更内容
* ────────────────────────────────────────────────────────────────────
* V1.1.1  2020-10-23    N/A	     初版
*
* Copyright (c) 2020 udrive Corporation. All rights reserved.
*┌────────────────────────────────────────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　      │
*│　版权所有：南京驭行科技有限公司　　　　　　　　　 　　　                │
*└────────────────────────────────────────────────────────────────────┘
*/
#ifndef DOCTOR_MODE_H_
#define DOCTOR_MODE_H_

#include <iostream>
#include <stdlib.h>
#include <ros/ros.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>

#include <deque> 

#include <geometry_msgs/Twist.h>
#include <std_msgs/Bool.h>

#include <riki_msgs/yx_path_control.h>
#include <riki_msgs/doctor_mode_status.h>
#include <actionlib/client/simple_action_client.h>
#include <move_base_msgs/MoveBaseAction.h>
#include <cstdlib>
#include <actionlib_msgs/GoalID.h>
#include <std_msgs/Bool.h>
#include <nav_msgs/Path.h>

//产生a到b之间的数， 不包括a 和 b
#define random(a,b) (rand()%(b-a)+a)


using namespace std;

//using namespace actionlib_tutorials;
typedef actionlib::SimpleActionClient<move_base_msgs::MoveBaseAction> Client;
typedef boost::shared_ptr< ::move_base_msgs::MoveBaseResult const > MoveBaseResultConstPtr;

//cmd定义
#define START_FIX_ROUTE         	1      //开始固定路线消毒
#define PAUSE_FIX_ROUTE         	2      //暂停固定路线消毒
#define CONTINUE_FIX_ROUTE         	3      //暂停固定路线消毒
#define STOP_FIX_ROUTE          	4      //停止固定路线消毒
#define COMPLETE_FIX_ROUTE          5      //完成固定路线消毒


class DoctorMode 
{
public:
	/**
	 * @说明   构造函数
	 */
	DoctorMode();

	/**
	 * @说明   析构函数
	 */
	~DoctorMode();

	/**
	 * @说明	 move_base执行结束回调函数
	 */ 
	void doneCb(const actionlib::SimpleClientGoalState& state,const move_base_msgs::MoveBaseResultConstPtr& result);
	/**
	 * @说明	 move_base目标发送成功后回调
	 */ 
	void ActiveCb();
	/**
	 * @说明	 move_base目标完成情况实时返回
	 */ 
	void FeedbackCb(const move_base_msgs::MoveBaseFeedbackConstPtr & feedback);


	/**
	 * @说明	 消毒模式线程
	 */ 
	void doctorThread();
	
	/**
	 * @说明	 消毒服务回调函数
	 */ 
	bool doctor_mode_call_back(riki_msgs::yx_path_control::Request  &req,riki_msgs::yx_path_control::Response &res);
	
	void nav_status_flag_call_back(std_msgs::Bool msg);
	
	double perpendicularDistance(geometry_msgs::PoseStamped curPose, double A, double B, double C);
	
	// DouglasPeucker 算法
	int DouglasPeucker(std::vector<geometry_msgs::PoseStamped>& Points, int Start, int End, double epsilon,std::vector<int>& indexs);
	
	inline double normal_angle(double angle)
	{
		if(angle<3.14159)
			return angle;
		else
			return angle - 3.14159;
	}
	
private:
	log4cpp::Category& log = log4cpp::Category::getRoot(); //日志句柄 

	ros::ServiceServer doctor_mode; //服务句柄
	ros::Publisher  doctor_mode_state_; //消毒状态
	Client ac;//move_base action请求句柄

	riki_msgs::doctor_mode_status docotr_msgs;
	std::deque<geometry_msgs::PoseStamped> mfixed_route_plan_;
	std::vector<int> reach_seq;

	bool send_flag ;
	bool end_pose;
	int current_point_subscript;
	bool time_out_flag;

	double segment_length;
	double distance_threshold;
	double angle_threshold;

	//set up the doctor's thread
	bool runPlanner_;
	boost::recursive_mutex doctor_mutex_;
	boost::condition_variable_any doctor_cond_;
	geometry_msgs::PoseStamped planner_goal_;
	boost::thread* doctor_thread_;
	
	boost::shared_ptr<geometry_msgs::Pose const> sharePose;
	
	bool skip_flag_;
	
	ros::Subscriber nav_status_sub_;
	
	//调试 可视化
	ros::Publisher original_path_;
	ros::Publisher dp_path_;
	ros::Publisher filter_path_;
	ros::Publisher reached_path_;
	
	double skip_wait_time;
	ros::Time last_valid_time;
};

#endif
