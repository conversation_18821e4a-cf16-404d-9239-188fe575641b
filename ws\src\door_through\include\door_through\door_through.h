#ifndef DOOR_THROUGH_CPP
#define DOOR_THROUGH_CPP

#include <ros/ros.h>
#include <actionlib/client/simple_action_client.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>

#include <iostream>
#include <stdlib.h>
#include <string>

#include <std_srvs/Empty.h>
#include <std_msgs/UInt8.h>
#include <std_msgs/Bool.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/PoseStamped.h>
#include <nav_msgs/Path.h>
#include <nav_msgs/GetPlan.h>
#include <actionlib/client/simple_action_client.h>
#include <move_base_msgs/MoveBaseAction.h>
#include <riki_msgs/yx_door_control.h>

//using namespace actionlib_tutorials;
typedef actionlib::SimpleActionClient<move_base_msgs::MoveBaseAction> Client;
typedef boost::shared_ptr< ::move_base_msgs::MoveBaseResult const > MoveBaseResultConstPtr;

class DoorThrough
{
public:
    DoorThrough();
    virtual ~DoorThrough();

    /**
    * @说明	 move_base执行结束回调函数
    */ 
    void doneCb(const actionlib::SimpleClientGoalState& state, const move_base_msgs::MoveBaseResultConstPtr& result);
    /**
    * @说明	 move_base目标发送成功后回调
    */ 
    void ActiveCb();
    /**
    * @说明	 move_base目标完成情况实时返回
    */ 
    void FeedbackCb(const move_base_msgs::MoveBaseFeedbackConstPtr &feedback);

    /**
    * @说明	 无动态障碍物路径获取服务回调障碍物
    */ 
    bool original_path_callback(riki_msgs::yx_door_control::Request  &req,riki_msgs::yx_door_control::Response &res);

private:
    ros::ServiceServer original_path_srv_;     //  获取路径服务
    ros::ServiceClient map_clear_client;;      //  清空地图
    ros::ServiceClient make_plan_client_;      //  路径计算

    ros::Publisher path_pub_ ;                 //可视化路径
    ros::Publisher access_control_pub_;        //门禁控制
    ros::Publisher door_through_pub_;        //过门状态
    ros::Publisher motor_control_pub_;        //电机控制

    Client ac;//move_base action请求句柄

    boost::shared_ptr<geometry_msgs::Pose const> sharePose;   //机器人位姿

    log4cpp::Category& log = log4cpp::Category::getRoot(); //日志句柄 

    int door_through_state;

    bool debug_;               //调试开关

};





#endif