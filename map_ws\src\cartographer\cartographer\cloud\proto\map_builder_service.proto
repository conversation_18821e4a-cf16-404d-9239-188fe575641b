// Copyright 2017 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/pose_graph.proto";
import "cartographer/mapping/proto/serialization.proto";
import "cartographer/mapping/proto/submap_visualization.proto";
import "cartographer/mapping/proto/trajectory_builder_options.proto";
import "cartographer/sensor/proto/sensor.proto";
import "cartographer/transform/proto/transform.proto";
import "google/protobuf/empty.proto";

package cartographer.cloud.proto;

enum SensorType {
  RANGE = 0;
  IMU = 1;
  ODOMETRY = 2;
  FIXED_FRAME_POSE = 3;
  LANDMARK = 4;
  LOCAL_SLAM_RESULT = 5;
}

enum TrajectoryState {
  ACTIVE = 0;
  FINISHED = 1;
  FROZEN = 2;
  DELETED = 3;
}

message SensorId {
  string id = 1;
  SensorType type = 2;
}

message AddTrajectoryRequest {
  repeated SensorId expected_sensor_ids = 3;
  cartographer.mapping.proto.TrajectoryBuilderOptions
      trajectory_builder_options = 2;
  string client_id = 4;
}

message SensorMetadata {
  int32 trajectory_id = 1;
  string sensor_id = 2;
  string client_id = 3;
}

message SensorData {
  SensorMetadata sensor_metadata = 1;
  oneof sensor_data {
    cartographer.sensor.proto.OdometryData odometry_data = 2;
    cartographer.sensor.proto.ImuData imu_data = 3;
    cartographer.sensor.proto.TimedPointCloudData timed_point_cloud_data = 4;
    cartographer.sensor.proto.FixedFramePoseData fixed_frame_pose_data = 5;
    cartographer.sensor.proto.LandmarkData landmark_data = 6;
    cartographer.mapping.proto.LocalSlamResultData local_slam_result_data = 7;
  }
}

message AddTrajectoryResponse {
  int32 trajectory_id = 1;
}

message AddSensorDataBatchRequest {
  repeated SensorData sensor_data = 1;
}

message AddOdometryDataRequest {
  SensorMetadata sensor_metadata = 1;
  cartographer.sensor.proto.OdometryData odometry_data = 2;
}

message AddImuDataRequest {
  SensorMetadata sensor_metadata = 1;
  cartographer.sensor.proto.ImuData imu_data = 2;
}

message AddRangefinderDataRequest {
  SensorMetadata sensor_metadata = 1;
  cartographer.sensor.proto.TimedPointCloudData timed_point_cloud_data = 2;
}

message AddFixedFramePoseDataRequest {
  SensorMetadata sensor_metadata = 1;
  cartographer.sensor.proto.FixedFramePoseData fixed_frame_pose_data = 2;
}

message AddLandmarkDataRequest {
  SensorMetadata sensor_metadata = 1;
  cartographer.sensor.proto.LandmarkData landmark_data = 2;
}

message FinishTrajectoryRequest {
  int32 trajectory_id = 1;
  string client_id = 2;
}

message DeleteTrajectoryRequest {
  int32 trajectory_id = 1;
  string client_id = 2;
}

message ReceiveLocalSlamResultsRequest {
  int32 trajectory_id = 1;
}

message LocalSlamInsertionResult {
  cartographer.mapping.proto.NodeId node_id = 1;
}

message ReceiveLocalSlamResultsResponse {
  int32 trajectory_id = 1;
  int64 timestamp = 2;
  cartographer.transform.proto.Rigid3d local_pose = 3;
  cartographer.sensor.proto.RangeData range_data = 4;
  LocalSlamInsertionResult insertion_result = 5;
}

message ReceiveGlobalSlamOptimizationsResponse {
  map<int32 /* trajectory_id */, cartographer.mapping.proto.NodeId>
      last_optimized_node_ids = 1;
  map<int32 /* trajectory_id */, cartographer.mapping.proto.SubmapId>
      last_optimized_submap_ids = 2;
}

message GetSubmapRequest {
  cartographer.mapping.proto.SubmapId submap_id = 1;
}

message LoadStateRequest {
  oneof state_chunk {
    cartographer.mapping.proto.SerializedData serialized_data = 1;
    cartographer.mapping.proto.SerializationHeader serialization_header = 2;
    string client_id = 3;
  }
  bool load_frozen_state = 4;
}

message TrajectoryRemapping {
  map<int32 /* serialized trajectory id */, int32 /* trajectory id */>
      serialized_trajectories_to_trajectories = 1;
}

message LoadStateResponse {
  TrajectoryRemapping trajectory_remapping = 1;
}

message LoadStateFromFileRequest {
  string file_path = 1;
  string client_id = 2;
  bool load_frozen_state = 3;
}

message LoadStateFromFileResponse {
  TrajectoryRemapping trajectory_remapping = 1;
}

message GetSubmapResponse {
  cartographer.mapping.proto.SubmapQuery.Response submap_query_response = 1;
  string error_msg = 2;
}

message TrajectoryNodePose {
  message ConstantPoseData {
    int64 timestamp = 1;
    cartographer.transform.proto.Rigid3d local_pose = 2;
  }
  cartographer.mapping.proto.NodeId node_id = 1;
  cartographer.transform.proto.Rigid3d global_pose = 2;
  ConstantPoseData constant_pose_data = 3;
}

message GetTrajectoryNodePosesResponse {
  repeated TrajectoryNodePose node_poses = 1;
}

message GetTrajectoryStatesResponse {
  map<int32 /* trajectory_id */, TrajectoryState> trajectories_state = 1;
}

message GetLandmarkPosesResponse {
  repeated cartographer.mapping.proto.PoseGraph.LandmarkPose landmark_poses = 1;
}

message SetLandmarkPoseRequest {
  cartographer.mapping.proto.PoseGraph.LandmarkPose landmark_pose = 1;
}

message SubmapPose {
  cartographer.mapping.proto.SubmapId submap_id = 1;
  int32 submap_version = 2;
  cartographer.transform.proto.Rigid3d global_pose = 3;
}

message GetAllSubmapPosesResponse {
  repeated SubmapPose submap_poses = 1;
}

message GetLocalToGlobalTransformRequest {
  int32 trajectory_id = 1;
}

message GetLocalToGlobalTransformResponse {
  cartographer.transform.proto.Rigid3d local_to_global = 1;
}

message GetConstraintsResponse {
  repeated cartographer.mapping.proto.PoseGraph.Constraint constraints = 1;
}

message WriteStateResponse {
  oneof state_chunk {
    cartographer.mapping.proto.SerializationHeader header = 1;
    cartographer.mapping.proto.SerializedData serialized_data = 2;
  }
}

message WriteStateToFileRequest {
  string filename = 1;
}

message WriteStateToFileResponse {
  bool success = 1;
}

message IsTrajectoryFinishedRequest {
  int32 trajectory_id = 1;
}

message IsTrajectoryFinishedResponse {
  bool is_finished = 1;
}

message IsTrajectoryFrozenRequest {
  int32 trajectory_id = 1;
}

message IsTrajectoryFrozenResponse {
  bool is_frozen = 1;
}

service MapBuilderService {
  // Starts a new trajectory and returns its index.
  rpc AddTrajectory(AddTrajectoryRequest) returns (AddTrajectoryResponse);

  // Adds a batch of sensor data  to a trajectory.
  rpc AddSensorDataBatch(AddSensorDataBatchRequest)
      returns (google.protobuf.Empty);

  // Adds odometry data from the sensor with id 'sensor_metadata.sensor_id' to
  // the trajectory corresponding to 'sensor_metadata.trajectory_id'.
  rpc AddOdometryData(stream AddOdometryDataRequest)
      returns (google.protobuf.Empty);

  // Same for IMU data.
  rpc AddImuData(stream AddImuDataRequest) returns (google.protobuf.Empty);

  // Same for range-finder data.
  rpc AddRangefinderData(stream AddRangefinderDataRequest)
      returns (google.protobuf.Empty);

  // Same for fixed-frame pose data.
  rpc AddFixedFramePoseData(stream AddFixedFramePoseDataRequest)
      returns (google.protobuf.Empty);

  // Same for landmark data.
  rpc AddLandmarkData(stream AddLandmarkDataRequest)
      returns (google.protobuf.Empty);

  // Requests the server to send a stream of local SLAM results for the given
  // 'trajectory_id'.
  rpc ReceiveLocalSlamResults(ReceiveLocalSlamResultsRequest)
      returns (stream ReceiveLocalSlamResultsResponse);

  // Requests the server to send a stream of global SLAM notifications.
  rpc ReceiveGlobalSlamOptimizations(google.protobuf.Empty)
      returns (stream ReceiveGlobalSlamOptimizationsResponse);

  // Marks a trajectory corresponding to 'trajectory_id' as finished,
  // i.e. no further sensor data is expected.
  rpc FinishTrajectory(FinishTrajectoryRequest) returns (google.protobuf.Empty);

  // Deletes a trajectory asynchronously.
  rpc DeleteTrajectory(DeleteTrajectoryRequest) returns (google.protobuf.Empty);

  // Retrieves a single submap.
  rpc GetSubmap(GetSubmapRequest) returns (GetSubmapResponse);

  // Returns the current optimized trajectory poses.
  rpc GetTrajectoryNodePoses(google.protobuf.Empty)
      returns (GetTrajectoryNodePosesResponse);

  // Returns the states of trajectories.
  rpc GetTrajectoryStates(google.protobuf.Empty)
      returns (GetTrajectoryStatesResponse);

  // Returns the current optimized landmark poses.
  rpc GetLandmarkPoses(google.protobuf.Empty)
      returns (GetLandmarkPosesResponse);

  // Returns the current optimized submap poses.
  rpc GetAllSubmapPoses(google.protobuf.Empty)
      returns (GetAllSubmapPosesResponse);

  // Returns the current local-to-global transform for the trajectory.
  rpc GetLocalToGlobalTransform(GetLocalToGlobalTransformRequest)
      returns (GetLocalToGlobalTransformResponse);

  // Returns the list of constraints in the current optimization problem.
  rpc GetConstraints(google.protobuf.Empty) returns (GetConstraintsResponse);

  // Sets a pose for a landmark.
  rpc SetLandmarkPose(SetLandmarkPoseRequest) returns (google.protobuf.Empty);

  // Checks whether the trajectory is finished.
  rpc IsTrajectoryFinished(IsTrajectoryFinishedRequest)
      returns (IsTrajectoryFinishedResponse);

  // Checks whether the trajectory is frozen.
  rpc IsTrajectoryFrozen(IsTrajectoryFrozenRequest)
      returns (IsTrajectoryFrozenResponse);

  // Requests a PoseGraph to call RunFinalOptimization.
  rpc RunFinalOptimization(google.protobuf.Empty)
      returns (google.protobuf.Empty);

  // Adds serialized SLAM state data in the order defined by ProtoStreamReader.
  rpc LoadState(stream LoadStateRequest) returns (LoadStateResponse);

  // Loads a serialized SLAM state data from the host file system.
  rpc LoadStateFromFile(LoadStateFromFileRequest)
      returns (LoadStateFromFileResponse);

  // Receives serialized SLAM state data in the order defined by
  // ProtoStreamWriter.
  rpc WriteState(google.protobuf.Empty) returns (stream WriteStateResponse);

  // Writes the serialized SLAM state to the host file system.
  rpc WriteStateToFile(WriteStateToFileRequest)
      returns (WriteStateToFileResponse);
}
