/**********************************************************************************
 *  文件名：      path_record.cpp
 *  版权：        南京驭行科技有限公司
 *  描述：        路径录制程序
 *  修改人：       何世政
 *  修改时间：      2020-11-24
 *  修改内容：      新增
 **********************************************************************************/

#include <ros/ros.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <doctor_mode/doctor_mode.h>
#include <riki_msgs/yx_path_control.h>
#include <nav_msgs/GetPlan.h>
#include <nav_msgs/Path.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/PoseStamped.h>

using namespace std;

geometry_msgs::PoseStamped pose_tmp;

 

void robot_pose_call_back(const geometry_msgs::Pose &robot_pose_)
{
	pose_tmp.header.stamp = ros::Time::now();
	pose_tmp.header.frame_id = "map";
	pose_tmp.pose = robot_pose_;
	
	//log.info("收到机器人位姿数据");
	
}


int main(int argc, char** argv){
	ros::init(argc, argv, "path_record");
	
	ros::NodeHandle  nh;
	
	ros::Subscriber  rosbot_pose_sub_ = nh.subscribe("robot_pose", 10, robot_pose_call_back);
	ros::Publisher path_record_pub_ = nh.advertise<const nav_msgs::Path>("path_record", 10);
	
	// 读取解析配置文件基础路径
	string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	
    // 读取解析配置文件
    try
    {
        log4cpp::PropertyConfigurator::configure(config_base_path+"/singlechip_top_driver.conf");
    }
    catch (log4cpp::ConfigureFailure& f)
    {
        std::cout << "Configure Problem: " << f.what() << std::endl;
    }
	
	log4cpp::Category& log = log4cpp::Category::getRoot(); //日志句柄
	
	sleep(3);
	
	geometry_msgs::PoseStamped last_pose_tmp = pose_tmp;;
	
	nav_msgs::Path path_;
	
	ros::Rate loop_rate(1);
    while(ros::ok())
    {
    	ros::spinOnce();
		
		log.info("last_pose_tmp:  %d %d ",last_pose_tmp.pose.position.x,last_pose_tmp.pose.position.y);
		log.info("last_pose_tmp:  %d %d ",pose_tmp.pose.position.x,pose_tmp.pose.position.y);
		int i =0;
		if(hypot((last_pose_tmp.pose.position.x - pose_tmp.pose.position.x),(last_pose_tmp.pose.position.y - pose_tmp.pose.position.y))>0.2)
		{
			i++;
			path_.poses.push_back(pose_tmp);
			log.info("路径新增点 %d------------------------------------------------------",i);
		}
		
		path_record_pub_.publish(path_);
		
		last_pose_tmp = pose_tmp;
		
		loop_rate.sleep();
	}
	
	ros::spin();
	return 0;
}

