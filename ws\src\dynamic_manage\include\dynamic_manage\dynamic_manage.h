/**********************************************************************************
 *  文件名：       dynamic_manage.h
 *  版权�?       南京驭行科技有限公司
 *  描述：�?      动态调参类定义
 *  修改人：       李冬�?
 *  修改时间�?     2019-12-31
 *  修改内容�?     新增
 **********************************************************************************/
#include <iostream>
#include <stdlib.h>
#include <algorithm>
#include <ros/ros.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <geometry_msgs/Pose.h>
#include <dwa_local_planner/DWAPlannerConfig.h>
#include "client.h"
#include <ros/package.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <visualization_msgs/Marker.h>
#include <nav_msgs/Odometry.h>
#include <tf/transform_listener.h>
#include <riki_msgs/robot_status.h>
#include <riki_msgs/robot_nav_status.h>
#include <riki_msgs/robot_speed_area.h>
#include <riki_msgs/speedAreas.h>

#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Point.h>
#include <people_msgs/PositionMeasurementArray.h>

using namespace std;

enum SpeedLevel {LOW_1,LOW_2,LOW_3,LOW_4,LOW_5,NORMAL};
enum RotateLevel { RLOW = 1, RNORMAL, RFAST };

class DynamicManage
{
	public:
	/**
	 * @说明   构造函�?
	 */
	DynamicManage();

	/**
	 * @说明   析构函数
	 */
	~DynamicManage();

	/**
	 * @说明   位置信息回调函数
	 */
	void robot_pose_call_back(geometry_msgs::Pose msg);

	/**
	 * @说明	 行人位置信息回调函数
	 */
	//void leg_pose_call_back(visualization_msgs::Marker msg);
	void leg_pose_call_back(people_msgs::PositionMeasurementArray msg);

	void update_speed_level(bool &speed_charge_flag, int &speed_level);
	/**

	 * @说明	 更新旋转速度

	 */

	void update_rotate_level(bool &rotate_charge_flag, int &rotate_level);

	/**
	 * @说明	 odom位置信息回调函数
	 */
	//void odom_pose_call_back(const nav_msgs::Odometry::ConstPtr& odom);
	void robot_nav_status_back(const riki_msgs::robot_nav_status & msg);
	/**

	 * @说明	 订阅速度

	 */

	void robot_cmd_vel_back(const geometry_msgs::Twist & msg);

	bool speed_area_back(riki_msgs::speedAreas::Request &req, riki_msgs::speedAreas::Response &res);

	void speed_change(bool &speed_change, double &speed_line, double &speed_angular);
	
	bool pointInPolygon(std::vector<std::vector<geometry_msgs::Point>> speed_areas_polygons, double x, double y, int& i);

private:
	log4cpp::Category& log = log4cpp::Category::getRoot(); //日志句柄 
	geometry_msgs::Pose robot_pose_map_; //存储当前map坐标系位�?
	ros::Subscriber robot_pose_sub_; //订阅位置信息
	ros::Subscriber leg_pose_sub_ ,nav_states_sub_; //订阅位置信息
	ros::Subscriber robot_cmd_sub_ ;
	ros::Publisher  robot_states_pub_;
	ros::ServiceServer speed_areas_ser_;   
	
	geometry_msgs::PoseStamped pose_base_;
	geometry_msgs::PoseStamped robot_pose_;
	int speed_level_;
	bool speed_charge_flag_;
	bool find_people_;
	int people_id_;
	int last_msg_id_;
	int nav_states_=0;
    int dist_tmp = 1000;
    int dist_people;
    ros::Subscriber  cmd_vel_sub_;
    int rotate_level_;
	bool rotate_charge_flag_;
	float vel_x,vel_z;

	std::vector< std::vector<geometry_msgs::Point> > speed_areas;
	
	std::vector<double> max_line_speed;
	std::vector<double> max_angualr_speed;
	
	std::vector<geometry_msgs::PoseStamped> people_pose_;
	
	ros::Subscriber location_accuracy_ ;
};



