/**********************************************************************************
 *  文件名：       doctor_mode_node_test.cpp
 *  版权：        南京驭行科技有限公司
 *  描述：        消毒模式测试程序
 *  修改人：       何世政
 *  修改时间：      2020-10-24
 *  修改内容：      新增
 **********************************************************************************/

#include <ros/ros.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <doctor_mode/doctor_mode.h>
#include <riki_msgs/yx_path_control.h>
#include <nav_msgs/GetPlan.h>
#include <nav_msgs/Path.h>
#include <boost/filesystem.hpp>
#include "boost/thread.hpp"
#include "boost/thread/condition_variable.hpp"
#include "boost/thread/mutex.hpp"


using namespace std;


int main(int argc, char** argv){
	
	ros::init(argc, argv, "doctor_mode_node_test");
	ros::NodeHandle n;
	ros::ServiceClient client = n.serviceClient<riki_msgs::yx_path_control>("/doctor_mode_node/doctor_mode");
	ros::ServiceClient make_plan_client_ = n.serviceClient<nav_msgs::GetPlan>("move_base/make_plan");
	
	if (argc != 2)
	{
		ROS_INFO("usage: cmd");
	    return 1;	
	}
	else
	{
		int cmd_ = atoll(argv[1]);
		
		if(cmd_ == 10)
		{
			riki_msgs::yx_path_control srv;
			srv.request.cmd = 1;
			boost::shared_ptr<nav_msgs::Path const> path_record;    
			path_record = ros::topic::waitForMessage<nav_msgs::Path>("path_record",ros::Duration(4));
			if(path_record == NULL){
				return -1;
			}
			else
			{
				geometry_msgs::Pose pose_;
				
				for(int i=0;i<path_record->poses.size();i++)
				{
					pose_.position.x = path_record->poses[i].pose.position.x;
					pose_.position.y = path_record->poses[i].pose.position.y;
					pose_.position.z = path_record->poses[i].pose.position.z;
					pose_.orientation.x = path_record->poses[i].pose.orientation.x;
					pose_.orientation.y = path_record->poses[i].pose.orientation.y;
					pose_.orientation.z = path_record->poses[i].pose.orientation.z;
					pose_.orientation.w = path_record->poses[i].pose.orientation.w;
					srv.request.pose_info.push_back(pose_);
				}
			}
			sleep(30);
			if (client.call(srv))
			{
			  ;
			}
			else
			{
			  ROS_ERROR("Failed to call service doctor_mode_node_test");
			  return 1;
			}
		
			ROS_INFO("%d", srv.request.cmd);
			
			
		}
		
		
		riki_msgs::yx_path_control srv;
		srv.request.cmd = atoll(argv[1]);
		
		geometry_msgs::PoseStamped start_pose, end_pose;
			
		start_pose.header.frame_id = "map";
		start_pose.header.stamp = ros::Time::now();		
		start_pose.pose.position.x = 0.02;
		start_pose.pose.position.y = 1.3;
		start_pose.pose.position.z = 0.0 ;
		start_pose.pose.orientation.x = 0.0;
		start_pose.pose.orientation.y = 0.0;
		start_pose.pose.orientation.z = 0.0;
		start_pose.pose.orientation.w = 1.0;
		
		end_pose.header.frame_id = "map";
		end_pose.header.stamp = ros::Time::now();
		end_pose.pose.position.x = 7.6;
		end_pose.pose.position.y = -6.8;
		end_pose.pose.position.z = 0.0;
		end_pose.pose.orientation.x = 0.0;
		end_pose.pose.orientation.y = 0.0;
		end_pose.pose.orientation.z = 0.0;
		end_pose.pose.orientation.w = 1.0;
		
		nav_msgs::GetPlan get_plan_srv;

		get_plan_srv.request.start = start_pose;
		get_plan_srv.request.goal = end_pose;

		if(make_plan_client_.call(get_plan_srv))
		{
			ROS_INFO("make plan succeed");
			
			std::cout<<"路径点数："<<get_plan_srv.response.plan.poses.size()<<std::endl;
			
			geometry_msgs::Pose pose_;
			for(int i=0;i<get_plan_srv.response.plan.poses.size();i++)
			{
				pose_.position.x = get_plan_srv.response.plan.poses[i].pose.position.x;
				pose_.position.y = get_plan_srv.response.plan.poses[i].pose.position.y;
				pose_.position.z = get_plan_srv.response.plan.poses[i].pose.position.z;
				pose_.orientation.x = get_plan_srv.response.plan.poses[i].pose.orientation.x;
				pose_.orientation.y = get_plan_srv.response.plan.poses[i].pose.orientation.y;
				pose_.orientation.z = get_plan_srv.response.plan.poses[i].pose.orientation.z;
				pose_.orientation.w = get_plan_srv.response.plan.poses[i].pose.orientation.w;
				srv.request.pose_info.push_back(pose_);
			}
			
		}else{
			ROS_ERROR("make plan failed");
		}
		
		
		
		if (client.call(srv))
		{
		  ;
		}
		else
		{
		  ROS_ERROR("Failed to call service doctor_mode_node_test");
		  return 1;
		}
		
		ROS_INFO("%d", srv.request.cmd);
		return 0;
	}

}

