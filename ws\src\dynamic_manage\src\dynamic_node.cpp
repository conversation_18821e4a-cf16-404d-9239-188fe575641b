/**********************************************************************************
 *  文件名：       dynamic_node.cpp
 *  版权：        南京驭行科技有限公司
 *  描述：        动态调参
 *  修改人：       李冬琦
 *  修改时间：      2019-10-17
 *  修改内容：      新增
 **********************************************************************************/
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>


#include <ros/ros.h>
#include <ros/package.h>
#include <geometry_msgs/Pose.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <geometry_msgs/Pose.h>
#include <dwa_local_planner/DWAPlannerConfig.h>
#include "client.h"
using namespace std;
dwa_local_planner::DWAPlannerConfig g_config;

log4cpp::Category& mlog = log4cpp::Category::getRoot();

int g_curt = -1;
int g_int=-1;
int g_cha=-1;


void dynCallBack(const dwa_local_planner::DWAPlannerConfig &config)
{
    ROS_INFO("config.max_vel_x:%f",config.max_vel_x);
    ROS_INFO("config.max_vel_y:%f",config.max_vel_y);
    ROS_INFO("config.max_rot_vel:%f",config.max_rot_vel);
	g_config = config;
	g_int = 1;
}


void robot_pose_call_back(geometry_msgs::Pose msg)
{
	
//dynamic_reconfigure::Client<dwa_local_planner::DWAPlannerConfig> client("dynamic_srv", dynCallBack);

//mlog.info("pose_x:%f",msg.position.x);
//mlog.info("pose_y:%f",msg.position.y);
	if((msg.position.x<2.3 &&msg.position.x> -0.9&&msg.position.y<1 &&msg.position.y>-1 )
		||(msg.position.x<1.3 &&msg.position.x> -0.1&&msg.position.y<15 &&msg.position.y>12.7 )
		||(msg.position.x<6.9 &&msg.position.x> 4.5&&msg.position.y<15 &&msg.position.y>12.7 )
                ||(msg.position.x<7.8 &&msg.position.x> 5.7&&msg.position.y<1.35 &&msg.position.y>-0.25 )
		||(msg.position.x<7.9 &&msg.position.x> 5.9&&msg.position.y<6 &&msg.position.y>3.5 )
                ||(msg.position.x<-10 &&msg.position.x> -15&&msg.position.y<8 &&msg.position.y>5.2 )
		||(msg.position.x<-19 &&msg.position.x> -23&&msg.position.y<8 &&msg.position.y>5.2 )
		||(msg.position.x<-20 &&msg.position.x> -23&&msg.position.y<13 &&msg.position.y>11.2 )
                ||(msg.position.x<-21 &&msg.position.x> -24&&msg.position.y<8 &&msg.position.y>5.2 )
                ||(msg.position.x<-30 &&msg.position.x> -32&&msg.position.y<8 &&msg.position.y>5.2 )
)
	{
		if(g_curt == 1)
			g_cha = 1;
		g_curt = 2;
	}else{
		if(g_curt == 2)
			g_cha = 1;
		g_curt = 1;
	}

}

int main(int argc, char *argv[])
{
	//初始化日志
	//订阅speak主题
	//
	ros::init(argc, argv, "dynamic_node");
	ros::NodeHandle nh;
	string config_base_path = getenv("CONFIG_BASE_PATH");
	// 读取解析配置文件
	try
	{
		log4cpp::PropertyConfigurator::configure(config_base_path+"/dynamic.conf");
	}
	catch (log4cpp::ConfigureFailure& f)
	{
		std::cout << "Configure Problem: " << f.what() << std::endl;
	}
	
	ros::Subscriber sub = nh.subscribe("/robot_pose", 10, robot_pose_call_back);

   dynamic_reconfigure::Client<dwa_local_planner::DWAPlannerConfig> client("/move_base/DWAPlannerROS", dynCallBack);
	
	
	
	//client.setConfiguration(config);
	ros::Rate loop_rate(3);
	while(ros::ok())
    {
	    ros::spinOnce();
		if(g_int == 1&&g_cha == 1)
		{
			if(g_curt == 2)
			{
				mlog.info("max_vel_x:0.6");
				g_config.max_vel_x = 0.4;
				client.setConfiguration(g_config);
			}else
			{
				g_config.max_vel_x = 0.8;
				mlog.info("max_vel_x:0.8");
				client.setConfiguration(g_config);
			}
			g_cha = 0;
		}
		loop_rate.sleep();
	}
	ros::spin();

	
	return 0;
}



