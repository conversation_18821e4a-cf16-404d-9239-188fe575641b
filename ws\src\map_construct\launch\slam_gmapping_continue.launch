<launch>
  <node pkg="gmapping" type="slam_gmapping" name="slam_gmapping" output="screen" >
    <param name="base_frame" value="/base_link" /> <!--The frame attached to the mobile base -->
    <param name="odom_frame" value="/odom" /> <!-- The frame attached to the odometry system.  -->
    <param name="map_update_interval" value="0.5"/>  <!--0.1 地图更新的时间间隔。更新间隔越小，计算负荷越大，地图更新也受scanmatch的影响，如果scanmatch没有成功的话不会更新地图 default: 5.0 0.01--> 
    <param name="maxUrange" value="13.0"/> <!--探测最大可用范围，计光束能到的范围default80.0 A2 16m --> 
    <!-- <param name="minRange" value="-0.5"/> -->
    <param name="sigma" value="0.05"/> <!-- end point匹配标准差 -->
    <param name="kernelSize" value="1"/> <!-- 用于查找对应的kernel size -->
    <param name="lstep" value="0.05"/> <!-- optimize机器人移动的初始值（距离） -->
    <param name="astep" value="0.05"/> <!-- optimize机器人移动的初始值（角度） -->
    <param name="iterations" value="8"/> <!-- icp的迭代次数 5-->
    <param name="lsigma" value="0.005"/> <!-- 用于扫描匹配概率的激光标准差 -->
    <param name="ogain" value="3.0"/> <!-- 似然估计为平滑重采样影响使用的gain -->
    <param name="lskip" value="0"/> <!-- 为0,表示所有的激光都处理，尽可能为零，如果计算压力过大，可以改成1 -->
    <param name="minimumScore" value="80"/> <!--20 200 /很重要，判断scanmatch是否成功的阈值，过高的话会使scanmatch失败，从而影响地图更新速率 -->
    <!-- 以下四个参数是运动模型的噪声参数 -->
    <param name="srr" value="0.15"/>    <!--平移时里程误差作为平移函数(rho/rho)-->
    <param name="srt" value="0.05"/>    <!--平移时的里程误差作为旋转函数 (rho/theta)-->
    <param name="str" value="0.15"/>    <!--旋转时的里程误差作为平移函数 (theta/rho)-->
    <param name="stt" value="0.05"/>    <!--旋转时的里程误差作为旋转函数 (theta/theta)-->
    <param name="linearUpdate" value="0.01"/> <!--0.05 机器人移动linearUpdate距离，进行scanmatch -->
    <param name="angularUpdate" value="0.0436"/> <!-- 机器人旋转angularUpdate角度，进行scanmatch -->
    <param name="temporalUpdate" value="2"/> <!--如果最新扫描处理比更新慢，则处理1次扫描，该值为负数时关闭基于时间的更新-->  
    <param name="resampleThreshold" value="0.4"/> <!--粒子重新采样的阈值。更高意味着更频繁的重采样--> 
    <param name="particles" value="120"/> <!--50 500 滤波器中粒子个数30--> 
    <param name="xmin" value="-1.0"/> <!--地图初始尺寸-100,-100,100,100--> 
    <param name="ymin" value="-1.0"/>
    <param name="xmax" value="1.0"/>
    <param name="ymax" value="1.0"/>
    <param name="delta" value="0.05"/> <!--0.02 地图分辨率--> 
    <param name="llsamplerange" value="0.01"/> <!-- 于似然计算的平移采样距离 -->
    <param name="llsamplestep" value="0.01"/> <!-- 用于似然计算的平移采样步长 -->
    <param name="lasamplerange" value="0.005"/> <!-- 用于似然计算的角度采样距离 -->
    <param name="lasamplestep" value="0.005"/> <!-- 用于似然计算的角度采样步长 -->
    <param name="transform_publish_period" value="0.1"/> <!-- 变换发布时间间隔 -->
	
	<remap from="/map" to="/udrive1/map" />
  </node>
</launch>
