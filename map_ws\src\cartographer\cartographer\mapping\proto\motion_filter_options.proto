// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

message MotionFilterOptions {
  // Threshold above which range data is inserted based on time.
  double max_time_seconds = 1;

  // Threshold above which range data is inserted based on linear motion.
  double max_distance_meters = 2;

  // Threshold above which range data is inserted based on rotational motion.
  double max_angle_radians = 3;
}
