#include <ros/ros.h>
#include <iostream>
#include <stdlib.h>
#include <std_msgs/Int16.h>
#include <std_msgs/UInt8.h>
#include <std_msgs/Bool.h>
#include <string>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
log4cpp::Category& mlog = log4cpp::Category::getRoot();
using namespace std;
int sounds_device_id ;
//声音设备号 1为Master 2为Speaker 3为PCM 4为Headphone
void sounds_control_back(const std_msgs::UInt8::ConstPtr& msg) //音量控制回调函数
{
	mlog.info("==================sounds_control_back function start!===================");
	int sounds = msg->data;
	string cmd_;
	if(sounds>100)
	{
		sounds = 100;
	}	
	if(sounds_device_id==1)
	{
		cmd_ = "sudo -E amixer set Master " + to_string(sounds) + "%" + " unmute ";
	}	
	if(sounds_device_id==2)
	{
		cmd_ = "sudo -E amixer set Speaker " + to_string(sounds) + "%" + " unmute ";
	}
	if(sounds_device_id==3)
	{
		cmd_ = "sudo -E amixer set PCM " + to_string(sounds) + "%" + " unmute ";
	}
	if(sounds_device_id==4)
	{
		cmd_ = "sudo -E amixer set Headphone " + to_string(sounds) + "%" + " unmute ";
	}
	system(cmd_.c_str() );
	mlog.info("发送音量控制：%s",cmd_.c_str());
}

int main(int argc, char** argv)
{
	try
    {
        log4cpp::PropertyConfigurator::configure("/home/<USER>/udrive_v1_1_1/ws/config/alsa_play.conf"); 
    }
    catch (log4cpp::ConfigureFailure& f)
    {
        std::cout << "Configure Problem " << f.what() << std::endl;
    }
	ros::init(argc, argv, "alsa_node");
	ros::NodeHandle nh;
	nh.getParam("sounds_device_id", sounds_device_id);
	mlog.info("sounds_device_id is:%d ;说明：1是Master ； 2是Speaker ； 3是PCM ； 4为Headphone",sounds_device_id);
	ros::Subscriber sounds_control_sub_;	
	sounds_control_sub_ = nh.subscribe("sounds_ctrl", 1, sounds_control_back);	
	system("sudo -E /home/<USER>/udrive_v1_1_1/ws/devel/lib/alsa_play/alsa_voice_node &");
	system("sudo -E /home/<USER>/udrive_v1_1_1/ws/devel/lib/alsa_play/alsa_voice_background_node &");
	ros::spin();
	return 0;
}
 