// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/mapping/proto/motion_filter_options.proto";
import "cartographer/mapping/proto/pose_extrapolator_options.proto";
import "cartographer/mapping/proto/scan_matching/ceres_scan_matcher_options_3d.proto";
import "cartographer/mapping/proto/scan_matching/real_time_correlative_scan_matcher_options.proto";
import "cartographer/mapping/proto/submaps_options_3d.proto";
import "cartographer/sensor/proto/adaptive_voxel_filter_options.proto";
import "cartographer/sensor/proto/sensor.proto";
import "cartographer/transform/proto/timestamped_transform.proto";

// NEXT ID: 22
message LocalTrajectoryBuilderOptions3D {
  // Rangefinder points outside these ranges will be dropped.
  float min_range = 1;
  float max_range = 2;

  // Number of range data to accumulate into one unwarped, combined range data
  // to use for scan matching.
  int32 num_accumulated_range_data = 3;

  // Voxel filter that gets applied to the range data immediately after
  // cropping.
  float voxel_filter_size = 4;

  // Voxel filter used to compute a sparser point cloud for matching.
  sensor.proto.AdaptiveVoxelFilterOptions
      high_resolution_adaptive_voxel_filter_options = 5;
  sensor.proto.AdaptiveVoxelFilterOptions
      low_resolution_adaptive_voxel_filter_options = 12;

  // Whether to solve the online scan matching first using the correlative scan
  // matcher to generate a good starting point for Ceres.
  bool use_online_correlative_scan_matching = 13;
  mapping.scan_matching.proto.RealTimeCorrelativeScanMatcherOptions
      real_time_correlative_scan_matcher_options = 14;
  mapping.scan_matching.proto.CeresScanMatcherOptions3D
      ceres_scan_matcher_options = 6;
  mapping.proto.MotionFilterOptions motion_filter_options = 7;

  // Time constant in seconds for the orientation moving average based on
  // observed gravity via the IMU. It should be chosen so that the error
  // 1. from acceleration measurements not due to gravity (which gets worse when
  // the constant is reduced) and
  // 2. from integration of angular velocities (which gets worse when the
  // constant is increased) is balanced.
  // TODO(schwoere,wohe): Remove this constant. This is only used for ROS and
  // was replaced by pose_extrapolator_options.
  double imu_gravity_time_constant = 15;

  // Number of histogram buckets for the rotational scan matcher.
  int32 rotational_histogram_size = 17;

  mapping.proto.PoseExtrapolatorOptions pose_extrapolator_options = 18;

  repeated transform.proto.TimestampedTransform initial_poses = 19;
  repeated sensor.proto.ImuData initial_imu_data = 20;

  SubmapsOptions3D submaps_options = 8;

  // Whether to use Lidar intensities in Ceres Scan Matcher.
  bool use_intensities = 21;
}
