// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/grid_2d_options.proto";
import "cartographer/mapping/proto/range_data_inserter_options.proto";

package cartographer.mapping.proto;

message SubmapsOptions2D {
  // Number of range data before adding a new submap. Each submap will get twice
  // the number of range data inserted: First for initialization without being
  // matched against, then while being matched.
  int32 num_range_data = 1;
  GridOptions2D grid_options_2d = 2;
  RangeDataInserterOptions range_data_inserter_options = 3;
}
