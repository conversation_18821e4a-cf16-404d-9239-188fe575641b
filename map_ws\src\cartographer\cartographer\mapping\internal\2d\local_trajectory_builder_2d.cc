/*
 * Copyright 2016 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "cartographer/mapping/internal/2d/local_trajectory_builder_2d.h"

#include <limits>
#include <memory>

#include "absl/memory/memory.h"
#include "cartographer/metrics/family_factory.h"
#include "cartographer/sensor/range_data.h"

#include <math.h>
#include "cartographer/mapping/2d/probability_grid_range_data_inserter_2d.h"

namespace cartographer {
namespace mapping {
using ::cartographer::sensor::RangefinderPoint;

static auto* kLocalSlamLatencyMetric = metrics::Gauge::Null();
static auto* kLocalSlamRealTimeRatio = metrics::Gauge::Null();
static auto* kLocalSlamCpuRealTimeRatio = metrics::Gauge::Null();
static auto* kRealTimeCorrelativeScanMatcherScoreMetric =
    metrics::Histogram::Null();
static auto* kCeresScanMatcherCostMetric = metrics::Histogram::Null();
static auto* kScanMatcherResidualDistanceMetric = metrics::Histogram::Null();
static auto* kScanMatcherResidualAngleMetric = metrics::Histogram::Null();

/**
 * @brief 构造函数
 * 
 * @param[in] options 
 * @param[in] expected_range_sensor_ids 所有range类型的话题
 */
LocalTrajectoryBuilder2D::LocalTrajectoryBuilder2D(
    const proto::LocalTrajectoryBuilderOptions2D& options,
    const std::vector<std::string>& expected_range_sensor_ids, bool location_mode, PoseGraph* pose_graph)
    : options_(options),
      active_submaps_(options.submaps_options()),
      //active_globle_submaps_(options.submaps_options()), //ldq add
      motion_filter_(options_.motion_filter_options()),
      real_time_correlative_scan_matcher_(
          options_.real_time_correlative_scan_matcher_options()),
      ceres_scan_matcher_(options_.ceres_scan_matcher_options()),
      range_data_collator_(expected_range_sensor_ids) {
        location_mode_ = location_mode;
        pose_graph_ = (PoseGraph2D *)pose_graph;
      }

LocalTrajectoryBuilder2D::~LocalTrajectoryBuilder2D() {}

/**
 * @brief 先进行点云的旋转与z方向的滤波, 然后再进行体素滤波减少数据量
 * 
 * @param[in] transform_to_gravity_aligned_frame 将点云变换到原点处, 且姿态为0的坐标变换
 * @param[in] range_data 传入的点云
 * @return sensor::RangeData 处理后的点云 拷贝
 */
sensor::RangeData
LocalTrajectoryBuilder2D::TransformToGravityAlignedFrameAndFilter(
    const transform::Rigid3f& transform_to_gravity_aligned_frame,
    const sensor::RangeData& range_data) const {
  // Step: 5 将原点位于机器人当前位姿处的点云 转成 原点位于local坐标系原点处的点云, 再进行z轴上的过滤
  const sensor::RangeData cropped =
      sensor::CropRangeData(sensor::TransformRangeData(
                                range_data, transform_to_gravity_aligned_frame),
                            options_.min_z(), options_.max_z()); // param: min_z max_z
  // Step: 6 对点云进行体素滤波
  return sensor::RangeData{
      cropped.origin,
      sensor::VoxelFilter(cropped.returns, options_.voxel_filter_size()), // param: voxel_filter_size
      sensor::VoxelFilter(cropped.misses, options_.voxel_filter_size())};
}

/**
 * @brief 进行扫描匹配
 * 
 * @param[in] time 点云的时间
 * @param[in] pose_prediction 先验位姿
 * @param[in] filtered_gravity_aligned_point_cloud 匹配用的点云
 * @return std::unique_ptr<transform::Rigid2d> 匹配后的二维位姿
 */
std::unique_ptr<transform::Rigid2d> LocalTrajectoryBuilder2D::ScanMatch(
    const common::Time time, const transform::Rigid2d& pose_prediction,
    const sensor::PointCloud& filtered_gravity_aligned_point_cloud) {
  if (active_submaps_.submaps().empty()) {
    return absl::make_unique<transform::Rigid2d>(pose_prediction);
  }
    // 使用active_submaps_的第一个子图进行匹配
  std::shared_ptr<const Submap2D> matching_submap =
      active_submaps_.submaps().front();
  /************************* modify by ldq 2022/11/07 ********************************/
  // 使用active_submaps_的第一个子图进行匹配
  //std::shared_ptr<const Submap2D> matching_submap;
  // //std::shared_ptr<const Submap2D> matching_submap;
  // ValueConversionTables conversion_tables; 
  //   // 地图初始大小,100个栅格
  // constexpr int kInitialSubmapSize = 100;
  // Eigen::Vector2f origin;
  // origin[0] = pose_prediction.translation().x();
  // origin[1] = pose_prediction.translation().y();
  // float resolution = options_.submaps_options().grid_options_2d().resolution(); // param: grid_options_2d.resolution
  // std::unique_ptr<GridInterface> create_grid = absl::make_unique<ProbabilityGrid>(
  //         MapLimits(resolution,
  //                   // 左上角坐标为坐标系的最大值, origin位于地图的中间
  //                   origin.cast<double>() + 0.5 * kInitialSubmapSize *
  //                                               resolution *
  //                                               Eigen::Vector2d::Ones(),
  //                   CellLimits(kInitialSubmapSize, kInitialSubmapSize)),
  //         &conversion_tables);
  // //std::unique_ptr<GridInterface> create_grid;
  // std::shared_ptr<Submap2D> matching_submap_tmp = absl::make_unique<Submap2D>(
  //     origin,
  //     std::unique_ptr<Grid2D>(
  //         static_cast<Grid2D*>(create_grid.release())),
  //     &conversion_tables);
  // //std::shared_ptr<Submap2D> matching_submap_tmp;
  // std::unique_ptr<RangeDataInserterInterface> range_data_inserter;
  
  // range_data_inserter = absl::make_unique<ProbabilityGridRangeDataInserter2D>(options_.submaps_options().range_data_inserter_options().probability_grid_range_data_inserter_options_2d());
  // //range_data_inserter = 
  if(location_mode_){
    matching_submap = matching_submaps_;
  //   // //matching_submap = 
  //   // // 只获取轨迹id 0 的轨迹
  //   // constexpr int trajectory_id = 0;
  //   // //1、获取所有节点
  //   // //pose_graph_ -> GetTrajectoryNodes();
  //   // std::shared_ptr<MapById<NodeId, TrajectoryNode>> trajectory_nodes =
  //   //   std::make_shared<MapById<NodeId, TrajectoryNode>>(pose_graph_->GetTrajectoryNodes());
  //   // //2、计算节点与当前位姿之间的距离
  //   // size_t trajectory_nodes_size = trajectory_nodes->SizeOfTrajectoryOrZero(trajectory_id);
  //   // //LOG(INFO) << "trajectory_nodes_size:"<<trajectory_nodes_size;
  //   // if (trajectory_nodes_size > 0){
  //   //   // 遍历轨迹0的所有优化后的节点
  //   //   auto node_it = trajectory_nodes->BeginOfTrajectory(trajectory_id);
  //   //   auto end_it = trajectory_nodes->EndOfTrajectory(trajectory_id);
  //   //   for (; node_it != end_it; ++node_it) {
  //   //     auto& trajectory_node = trajectory_nodes->at(node_it->id);
  //   //     auto& global_pose = trajectory_node.global_pose;
  //   //     auto& filtered_gravity_aligned_point_cloud = trajectory_node.constant_data->filtered_gravity_aligned_point_cloud;
    
  //   //     double dis_x = global_pose.translation().x()-pose_prediction.translation().x();
  //   //     double dis_y = global_pose.translation().y()-pose_prediction.translation().y();
  //   //     double dis_xy = sqrt(pow((dis_x),2)+pow((dis_y),2));
  //   //     //3、构建匹配地图
  //   //     if(dis_xy < 1){
  //   //       //LOG(INFO)<<"global_pose.translation().x="<<global_pose.translation().x()<<"  lobal_pose.translation().y="<<global_pose.translation().y();
  //   //       sensor::RangeData globle_range_data = sensor::RangeData{{}, {}, {}};
  //   //       //transform::Rigid3f aa = ;
  //   //       //auto& tf_point_cloud = 
  //   //       globle_range_data.returns = TransformPointCloud(filtered_gravity_aligned_point_cloud, global_pose.cast<float>());
  //   //       globle_range_data.origin = global_pose.cast<float>().translation();
  //   //       //LOG(INFO) << "trajectory_nodes_size:"<<trajectory_nodes_size;
  //   //       //active_globle_submaps_.InsertRangeData(globle_range_data);
  //   //       //matching_submap_tmp->InsertRangeData(globle_range_data, range_data_inserter.get());
  //   //           // 获取机器人当前姿态
  //   //       const transform::Rigid3d gravity_alignment = transform::Rigid3d::Rotation(
  //   //           extrapolator_->EstimateGravityOrientation(time));
  //   //       InsertIntoSubmap(
  //   //         time, globle_range_data, filtered_gravity_aligned_point_cloud,
  //   //       global_pose, gravity_alignment.rotation());
  //   //     }
  //   //   }
  //   // }
  //   // LOG(INFO) << "matching_submap by globle_range_data";
  //   // //matching_submap = active_submaps_.submaps().front();
  //   // matching_submap = matching_submap_tmp;
  //   // 只获取轨迹id 0 的轨迹
  //   //constexpr int trajectory_id = 0;
  //   //MapById<SubmapId, PoseGraphInterface::SubmapData>
    
  //   // for (auto it = submap_datas.begin(); it != submap_datas.end(); it ++) {
  //   //     it->grid();
  //   //     //matching_submap = it;
  //   // }
  //   // size_t submap_data_size = submap_datas.SizeOfTrajectoryOrZero(trajectory_id);
  //   // LOG(INFO) <<" submap_data.Size: "<< submap_data_size;
  //   // if (submap_data_size > 0){
  //   //   auto node_it = submap_datas.BeginOfTrajectory(trajectory_id);
  //   //   auto end_it = submap_datas.EndOfTrajectory(trajectory_id);  
  //   //   for (; node_it != end_it; ++node_it) {
  //   //     //pSon = static_cast<CSon*>(pFather); //是不安全的
  //   //     //std::shared_ptr<Submap2D> submap2D = std::dynamic_pointer_cast<Submap2D>(submap_datas.at(node_it->id).submap);
  //   //     auto submap2D = submap_datas.at(node_it->id).submap;
  //   //     //submap2D->submap_2d();
  //   //   }  
  //   // }
    
  //   constexpr int trajectory_id = 0;
  //   auto submap_datas_pose = pose_graph_->GetAllSubmapPoses();
  //   size_t submap_data_size = submap_datas_pose.SizeOfTrajectoryOrZero(trajectory_id);
  //   LOG(INFO) <<" submap_data.Size: "<< submap_data_size;
  //   int submap_index = 0; 
  //   transform::Rigid3d submap_index_pose;
  //   if (submap_data_size > 0){
  //     auto node_it = submap_datas_pose.BeginOfTrajectory(trajectory_id);
  //     auto end_it = submap_datas_pose.EndOfTrajectory(trajectory_id);
  //     double dis_xy_min = 0;
  //     auto ceres_pose_ok = absl::make_unique<transform::Rigid2d>(); 
  //     bool init_flag = false;
  //     for (; node_it != end_it; ++node_it) {
        
  //       auto ceres_pose_prediction = absl::make_unique<transform::Rigid2d>(); 
  //       //pSon = static_cast<CSon*>(pFather); //是不安全的
  //       //std::shared_ptr<Submap2D> submap2D = std::dynamic_pointer_cast<Submap2D>(submap_datas.at(node_it->id).submap);
  //       transform::Rigid3d submapPose2D = submap_datas_pose.at(node_it->id).pose;
  //       //submap2D->submap_2d();
  //       //LOG(INFO) <<" submapPose2D: "<< submapPose2D;
  //       double dis_x = submapPose2D.translation().x()-pose_prediction.translation().x();
  //       double dis_y = submapPose2D.translation().y()-pose_prediction.translation().y();
  //       double dis_xy = sqrt(pow((dis_x),2)+pow((dis_y),2));
  //       if(dis_xy < 10)
  //       {
  //         submap_index = node_it->id.submap_index;
  //         submap_index_pose = submapPose2D;
  //         // dis_xy_min = dis_xy;
  //         LOG(INFO) <<" matching_submap index: "<< submap_index;
  //         std::shared_ptr<Submap2D> matching_submap_tmp = pose_graph_->submaps().at(submap_index);
  //         transform::Rigid3d submap_index_posedd;
  //         matching_submap_tmp->update_local_pose(submap_index_pose);
          
          
  //       }

  //     }
  //   }
  //   // auto submap_datas = pose_graph_->submaps();
  //   // // for (std::shared_ptr<const Submap2D> submap_data: submap_datas) {
  //   // //   submap_data->local_pose();
  //   // // }
  //   // LOG(INFO) <<" matching_submap index: "<< submap_index;
  //   // std::shared_ptr<Submap2D> matching_submap_tmp = pose_graph_->submaps().at(submap_index);
  //   // transform::Rigid3d submap_index_posedd;
  //   // matching_submap_tmp->update_local_pose(submap_index_pose);
  //   //matching_submap = matching_submap_tmp;
  //   //matching_submap = active_submaps_.submaps().front();
  //   //matching_submap =matching_submaps_;
  //   matching_submap = matching_submap_tmp;
  }else{
    LOG(INFO) << "matching_submap by active_submaps";
    matching_submap = active_submaps_.submaps().front();
    //matching_submap = matching_submaps_;
  }
  /********************************* modify end ***************************************/
  // The online correlative scan matcher will refine the initial estimate for
  // the Ceres scan matcher.
  transform::Rigid2d initial_ceres_pose = pose_prediction;

  // 根据参数决定是否 使用correlative_scan_matching对先验位姿进行校准
  if (options_.use_online_correlative_scan_matching()) {
    const double score = real_time_correlative_scan_matcher_.Match(
        pose_prediction, filtered_gravity_aligned_point_cloud,
        *matching_submap->grid(), &initial_ceres_pose);
    kRealTimeCorrelativeScanMatcherScoreMetric->Observe(score);
  }

  auto pose_observation = absl::make_unique<transform::Rigid2d>();
  ceres::Solver::Summary summary;
  // 使用ceres进行扫描匹配
  ceres_scan_matcher_.Match(pose_prediction.translation(), initial_ceres_pose,
                            filtered_gravity_aligned_point_cloud,
                            *matching_submap->grid(), pose_observation.get(),
                            &summary);
  // 一些度量
  if (pose_observation) {
    kCeresScanMatcherCostMetric->Observe(summary.final_cost);
    const double residual_distance =
        (pose_observation->translation() - pose_prediction.translation())
            .norm();
    kScanMatcherResidualDistanceMetric->Observe(residual_distance);
    const double residual_angle =
        std::abs(pose_observation->rotation().angle() -
                 pose_prediction.rotation().angle());
    kScanMatcherResidualAngleMetric->Observe(residual_angle);
  }
  // 返回ceres计算后的位姿
  return pose_observation;
}

/**
 * @brief 处理点云数据, 进行扫描匹配, 将点云写成地图
 * 
 * @param[in] sensor_id 点云数据对应的话题名称
 * @param[in] unsynchronized_data 传入的点云数据
 * @return std::unique_ptr<LocalTrajectoryBuilder2D::MatchingResult> 匹配后的结果
 */
std::unique_ptr<LocalTrajectoryBuilder2D::MatchingResult>
LocalTrajectoryBuilder2D::AddRangeData(
    const std::string& sensor_id,
    const sensor::TimedPointCloudData& unsynchronized_data) {
  
  // Step: 1 进行多个雷达点云数据的时间同步, 点云的坐标是相对于tracking_frame的
  auto synchronized_data =
      range_data_collator_.AddRangeData(sensor_id, unsynchronized_data);
  if (synchronized_data.ranges.empty()) {
    LOG(INFO) << "Range data collator filling buffer.";
    return nullptr;
  }

  const common::Time& time = synchronized_data.time;
  // Initialize extrapolator now if we do not ever use an IMU.
  // 如果不用imu, 就在雷达这初始化位姿推测器
  if (!options_.use_imu_data()) {
    InitializeExtrapolator(time);
  }

  if (extrapolator_ == nullptr) {
    // Until we've initialized the extrapolator with our first IMU message, we
    // cannot compute the orientation of the rangefinder.
    LOG(INFO) << "Extrapolator not yet initialized.";
    return nullptr;
  }

  CHECK(!synchronized_data.ranges.empty());
  // TODO(gaschler): Check if this can strictly be 0.
  CHECK_LE(synchronized_data.ranges.back().point_time.time, 0.f);

  // 计算第一个点的时间
  const common::Time time_first_point =
      time +
      common::FromSeconds(synchronized_data.ranges.front().point_time.time);
  // 只有在extrapolator_初始化时, GetLastPoseTime()是common::Time::min()
  if (time_first_point < extrapolator_->GetLastPoseTime()) {
    LOG(INFO) << "Extrapolator is still initializing.";
    return nullptr;
  }

  std::vector<transform::Rigid3f> range_data_poses;
  range_data_poses.reserve(synchronized_data.ranges.size());
  bool warned = false;

  // 预测得到每一个时间点的位姿
  for (const auto& range : synchronized_data.ranges) {
    common::Time time_point = time + common::FromSeconds(range.point_time.time);
    // 如果该时间比上次预测位姿的时间还要早,说明这个点的时间戳往回走了, 就报错
    if (time_point < extrapolator_->GetLastExtrapolatedTime()) {
      // 一个循环只报一次错
      if (!warned) {
        LOG(ERROR)
            << "Timestamp of individual range data point jumps backwards from "
            << extrapolator_->GetLastExtrapolatedTime() << " to " << time_point;
        warned = true;
      }
      time_point = extrapolator_->GetLastExtrapolatedTime();
    }
    
    // Step: 2 预测出 每个点的时间戳时刻, tracking frame 在 local slam 坐标系下的位姿
    range_data_poses.push_back(
        extrapolator_->ExtrapolatePose(time_point).cast<float>());
  }

  if (num_accumulated_ == 0) {
    // 'accumulated_range_data_.origin' is uninitialized until the last
    // accumulation.
    accumulated_range_data_ = sensor::RangeData{{}, {}, {}};
  }

  // Drop any returns below the minimum range and convert returns beyond the
  // maximum range into misses.
  // 对每个数据点进行处理
  for (size_t i = 0; i < synchronized_data.ranges.size(); ++i) {
    // 获取在tracking frame 下点的坐标
    const sensor::TimedRangefinderPoint& hit =
        synchronized_data.ranges[i].point_time;
    // 将点云的origins坐标转到 local slam 坐标系下
    const Eigen::Vector3f origin_in_local =
        range_data_poses[i] *
        synchronized_data.origins.at(synchronized_data.ranges[i].origin_index);
    
    // Step: 3 运动畸变的去除, 将相对于tracking_frame的hit坐标 转成 local坐标系下的坐标
    sensor::RangefinderPoint hit_in_local =
        range_data_poses[i] * sensor::ToRangefinderPoint(hit);
    
    // 计算这个点的距离, 这里用的是去畸变之后的点的距离
    const Eigen::Vector3f delta = hit_in_local.position - origin_in_local;
    const float range = delta.norm();
    
    // param: min_range max_range
    if (range >= options_.min_range()) {
      if (range <= options_.max_range()) {
        // 在这里可以看到, returns里保存的是local slam下的去畸变之后的点的坐标
        accumulated_range_data_.returns.push_back(hit_in_local);
        //LOG(INFO) << "range returns";
      } else {
        //LOG(INFO) << "range miss";
        // Step: 4 超过max_range时的处理: 用一个距离进行替代, 并放入misses里
        hit_in_local.position =
            origin_in_local +
            // param: missing_data_ray_length, 是个比例, 不是距离
            options_.missing_data_ray_length() / range * delta;
        accumulated_range_data_.misses.push_back(hit_in_local);
      }
    }
  } // end for

  // 有一帧有效的数据了
  ++num_accumulated_;

  // param: num_accumulated_range_data 几帧有效的点云数据进行一次扫描匹配
  if (num_accumulated_ >= options_.num_accumulated_range_data()) {
    // 计算2次有效点云数据的的时间差
    const common::Time current_sensor_time = synchronized_data.time;
    absl::optional<common::Duration> sensor_duration;
    if (last_sensor_time_.has_value()) {
      sensor_duration = current_sensor_time - last_sensor_time_.value();
    }
    last_sensor_time_ = current_sensor_time;

    // 重置变量
    num_accumulated_ = 0;

    // 获取机器人当前姿态
    const transform::Rigid3d gravity_alignment = transform::Rigid3d::Rotation(
        extrapolator_->EstimateGravityOrientation(time));

    // TODO(gaschler): This assumes that 'range_data_poses.back()' is at time
    // 'time'.
    // 以最后一个点的时间戳估计出的坐标为这帧数据的原点
    accumulated_range_data_.origin = range_data_poses.back().translation();
    
    return AddAccumulatedRangeData(
        time,
        // 将点云变换到local原点处, 且姿态为0
        TransformToGravityAlignedFrameAndFilter(
            gravity_alignment.cast<float>() * range_data_poses.back().inverse(),
            accumulated_range_data_),
        gravity_alignment, sensor_duration);
  }

  return nullptr;
}

/**
 * @brief 进行扫描匹配, 将点云写入地图
 * 
 * @param[in] time 点云的时间戳
 * @param[in] gravity_aligned_range_data 原点位于local坐标系原点处的点云
 * @param[in] gravity_alignment 机器人当前姿态
 * @param[in] sensor_duration 2帧点云数据的时间差
 * @return std::unique_ptr<LocalTrajectoryBuilder2D::MatchingResult> 
 */
std::unique_ptr<LocalTrajectoryBuilder2D::MatchingResult>
LocalTrajectoryBuilder2D::AddAccumulatedRangeData(
    const common::Time time,
    const sensor::RangeData& gravity_aligned_range_data,
    const transform::Rigid3d& gravity_alignment,
    const absl::optional<common::Duration>& sensor_duration) {
  // 如果处理完点云之后数据为空, 就报错. 使用单线雷达时不要设置min_z
  if (gravity_aligned_range_data.returns.empty()) {
    LOG(WARNING) << "Dropped empty horizontal range data.";
    return nullptr;
  }

  // Computes a gravity aligned pose prediction.
  // 进行位姿的预测, 先验位姿
  const transform::Rigid3d non_gravity_aligned_pose_prediction =
      extrapolator_->ExtrapolatePose(time);
  // 将三维位姿先旋转到姿态为0, 再取xy坐标将三维位姿转成二维位姿
  const transform::Rigid2d pose_prediction = transform::Project2D(
      non_gravity_aligned_pose_prediction * gravity_alignment.inverse());

  // Step: 7 对 returns点云 进行自适应体素滤波，返回的点云的数据类型是PointCloud
  const sensor::PointCloud& filtered_gravity_aligned_point_cloud =
      sensor::AdaptiveVoxelFilter(gravity_aligned_range_data.returns,
                                  options_.adaptive_voxel_filter_options());
  if (filtered_gravity_aligned_point_cloud.empty()) {
    return nullptr;
  }

  // local map frame <- gravity-aligned frame
  // 扫描匹配, 进行点云与submap的匹配
  std::unique_ptr<transform::Rigid2d> pose_estimate_2d =
      ScanMatch(time, pose_prediction, filtered_gravity_aligned_point_cloud);

  if (pose_estimate_2d == nullptr) {
    LOG(WARNING) << "Scan matching failed.";
    return nullptr;
  }

  //LOG(INFO)<<"旋转之前:"<<pose_estimate_2d->translation().x()<<", "<<pose_estimate_2d->translation().y();
  // 将二维坐标旋转回之前的姿态
  const transform::Rigid3d pose_estimate =
      transform::Embed3D(*pose_estimate_2d) * gravity_alignment;
  //LOG(INFO)<<"旋转之后:"<<pose_estimate.translation().x()<<", "<<pose_estimate.translation().y();
  // 校准位姿估计器
  extrapolator_->AddPose(time, pose_estimate);

  // Step: 8 将 原点位于local坐标系原点处的点云 变换成 原点位于匹配后的位姿处的点云
  sensor::RangeData range_data_in_local =
      TransformRangeData(gravity_aligned_range_data,
                         transform::Embed3D(pose_estimate_2d->cast<float>()));
  
  // 将校正后的雷达数据写入submap
  std::unique_ptr<InsertionResult> insertion_result = InsertIntoSubmap(
      time, range_data_in_local, filtered_gravity_aligned_point_cloud,
      pose_estimate, gravity_alignment.rotation());

  // 计算耗时
  const auto wall_time = std::chrono::steady_clock::now();
  if (last_wall_time_.has_value()) {
    const auto wall_time_duration = wall_time - last_wall_time_.value();
    kLocalSlamLatencyMetric->Set(common::ToSeconds(wall_time_duration));
    if (sensor_duration.has_value()) {
      kLocalSlamRealTimeRatio->Set(common::ToSeconds(sensor_duration.value()) /
                                   common::ToSeconds(wall_time_duration));
    }
  }
  // 计算cpu耗时
  const double thread_cpu_time_seconds = common::GetThreadCpuTimeSeconds();
  if (last_thread_cpu_time_seconds_.has_value()) {
    const double thread_cpu_duration_seconds =
        thread_cpu_time_seconds - last_thread_cpu_time_seconds_.value();
    if (sensor_duration.has_value()) {
      kLocalSlamCpuRealTimeRatio->Set(
          common::ToSeconds(sensor_duration.value()) /
          thread_cpu_duration_seconds);
    }
  }
  last_wall_time_ = wall_time;
  last_thread_cpu_time_seconds_ = thread_cpu_time_seconds;

  // 返回结果 
  return absl::make_unique<MatchingResult>(
      MatchingResult{time, pose_estimate, std::move(range_data_in_local),
                     std::move(insertion_result)});
}

/**
 * @brief 将处理后的雷达数据写入submap
 * 
 * @param[in] time 点云的时间
 * @param[in] range_data_in_local 校正后的点云
 * @param[in] filtered_gravity_aligned_point_cloud 自适应体素滤波后的点云
 * @param[in] pose_estimate 扫描匹配后的三维位姿
 * @param[in] gravity_alignment 
 * @return std::unique_ptr<LocalTrajectoryBuilder2D::InsertionResult> 
 */
std::unique_ptr<LocalTrajectoryBuilder2D::InsertionResult>
LocalTrajectoryBuilder2D::InsertIntoSubmap(
    const common::Time time, const sensor::RangeData& range_data_in_local,
    const sensor::PointCloud& filtered_gravity_aligned_point_cloud,
    const transform::Rigid3d& pose_estimate,
    const Eigen::Quaterniond& gravity_alignment) {
  // 如果移动距离过小, 或者时间过短, 不进行地图的更新
  if (motion_filter_.IsSimilar(time, pose_estimate)) {
    return nullptr;
  }
  // 将点云数据写入到submap中
  std::vector<std::shared_ptr<const Submap2D>> insertion_submaps =
      active_submaps_.InsertRangeData(range_data_in_local);

  // ldq add
  // std::unique_ptr<RangeDataInserterInterface> range_data_inserter;
  // range_data_inserter = absl::make_unique<ProbabilityGridRangeDataInserter2D>(options_.submaps_options().range_data_inserter_options().probability_grid_range_data_inserter_options_2d());
  // matching_submaps_->InsertRangeData(range_data_in_local, range_data_inserter.get());

  // 生成InsertionResult格式的数据进行返回
  return absl::make_unique<InsertionResult>(InsertionResult{
      std::make_shared<const TrajectoryNode::Data>(TrajectoryNode::Data{
          time,
          gravity_alignment,
          filtered_gravity_aligned_point_cloud,  // 这里存的是体素滤波后的点云, 不是校准后的点云
          {},  // 'high_resolution_point_cloud' is only used in 3D.
          {},  // 'low_resolution_point_cloud' is only used in 3D.
          {},  // 'rotational_scan_matcher_histogram' is only used in 3D.
          pose_estimate}),
      std::move(insertion_submaps)});
}

// 将IMU数据加入到Extrapolator中
void LocalTrajectoryBuilder2D::AddImuData(const sensor::ImuData& imu_data) {
  CHECK(options_.use_imu_data()) << "An unexpected IMU packet was added.";
  InitializeExtrapolator(imu_data.time);
  extrapolator_->AddImuData(imu_data);
}

// 将里程计数据加入到Extrapolator中
void LocalTrajectoryBuilder2D::AddOdometryData(
    const sensor::OdometryData& odometry_data) {
  if (extrapolator_ == nullptr) {
    // Until we've initialized the extrapolator we cannot add odometry data.
    LOG(INFO) << "Extrapolator not yet initialized.";
    return;
  }
  extrapolator_->AddOdometryData(odometry_data);
}

// 如果Extrapolator没有初始化就进行初始化
void LocalTrajectoryBuilder2D::InitializeExtrapolator(const common::Time time) {
  // 如果已经初始化过了就直接返回
  if (extrapolator_ != nullptr) {
    return;
  }

  // 注意 use_imu_based为true就会报错
  CHECK(!options_.pose_extrapolator_options().use_imu_based());
  // TODO(gaschler): Consider using InitializeWithImu as 3D does.

  // 初始化位姿推测器
  extrapolator_ = absl::make_unique<PoseExtrapolator>(
      ::cartographer::common::FromSeconds(options_.pose_extrapolator_options()
                                              .constant_velocity()
                                              .pose_queue_duration()), // 0.001s
      options_.pose_extrapolator_options()
          .constant_velocity()
          .imu_gravity_time_constant()); // 10
  /************************ modify by ldq 2022/11/11******************************/
  // 添加初始位姿
  //extrapolator_->AddPose(time, transform::Rigid3d::Identity());
  extrapolator_->AddPose(time, initial_extrapolator_pose_);
  /************************ modify end ********************************************/
}

void LocalTrajectoryBuilder2D::RegisterMetrics(
    metrics::FamilyFactory* family_factory) {
  auto* latency = family_factory->NewGaugeFamily(
      "mapping_2d_local_trajectory_builder_latency",
      "Duration from first incoming point cloud in accumulation to local slam "
      "result");
  kLocalSlamLatencyMetric = latency->Add({});
  auto* real_time_ratio = family_factory->NewGaugeFamily(
      "mapping_2d_local_trajectory_builder_real_time_ratio",
      "sensor duration / wall clock duration.");
  kLocalSlamRealTimeRatio = real_time_ratio->Add({});

  auto* cpu_real_time_ratio = family_factory->NewGaugeFamily(
      "mapping_2d_local_trajectory_builder_cpu_real_time_ratio",
      "sensor duration / cpu duration.");
  kLocalSlamCpuRealTimeRatio = cpu_real_time_ratio->Add({});
  auto score_boundaries = metrics::Histogram::FixedWidth(0.05, 20);
  auto* scores = family_factory->NewHistogramFamily(
      "mapping_2d_local_trajectory_builder_scores", "Local scan matcher scores",
      score_boundaries);
  kRealTimeCorrelativeScanMatcherScoreMetric =
      scores->Add({{"scan_matcher", "real_time_correlative"}});
  auto cost_boundaries = metrics::Histogram::ScaledPowersOf(2, 0.01, 100);
  auto* costs = family_factory->NewHistogramFamily(
      "mapping_2d_local_trajectory_builder_costs", "Local scan matcher costs",
      cost_boundaries);
  kCeresScanMatcherCostMetric = costs->Add({{"scan_matcher", "ceres"}});
  auto distance_boundaries = metrics::Histogram::ScaledPowersOf(2, 0.01, 10);
  auto* residuals = family_factory->NewHistogramFamily(
      "mapping_2d_local_trajectory_builder_residuals",
      "Local scan matcher residuals", distance_boundaries);
  kScanMatcherResidualDistanceMetric =
      residuals->Add({{"component", "distance"}});
  kScanMatcherResidualAngleMetric = residuals->Add({{"component", "angle"}});
}

/*********************** modify by ldq 2022/11/11*************************************/
//设置位姿推测器的初始位置，只在定位时启用
void LocalTrajectoryBuilder2D::SetInitialExtrapolatorPose(const transform::Rigid3d& pose){
  initial_extrapolator_pose_ = pose;
  if(!location_mode_){
    // ValueConversionTables conversion_tables;
    //   // 地图初始大小,100个栅格
    // constexpr int kInitialSubmapSize = 100;
    // float resolution = options_.submaps_options().grid_options_2d().resolution(); 
    // Eigen::Vector2f origin;
    // origin[0] = pose.translation().x();
    // origin[1] = pose.translation().y();
    // std::unique_ptr<GridInterface> create_grid = absl::make_unique<ProbabilityGrid>(
    //         MapLimits(resolution,
    //                   // 左上角坐标为坐标系的最大值, origin位于地图的中间
    //                   origin.cast<double>() + 0.5 * kInitialSubmapSize *
    //                                               resolution *
    //                                               Eigen::Vector2d::Ones(),
    //                   CellLimits(kInitialSubmapSize, kInitialSubmapSize)),
    //         &conversion_tables);
    // matching_submaps_ = absl::make_unique<Submap2D>(
    //   origin,
    //   std::unique_ptr<Grid2D>(
    //       static_cast<Grid2D*>(create_grid.release())),
    //   &conversion_tables);
    return;
  }
  // 只获取轨迹id 0 的轨迹
  constexpr int trajectory_id = 0;
  // //获取第一个子图
  //auto submap_datas = pose_graph_->submaps();
  matching_submaps_ = pose_graph_->submaps().front();
  matching_submaps_->set_insertion_finished(false);
  // //1、获取所有节点
  std::shared_ptr<MapById<NodeId, TrajectoryNode>> trajectory_nodes =
  std::make_shared<MapById<NodeId, TrajectoryNode>>(pose_graph_->GetTrajectoryNodes());
  // //std::shared_ptr<Submap2D> matching_submap_tmp;
  std::unique_ptr<RangeDataInserterInterface> range_data_inserter;
  
  range_data_inserter = absl::make_unique<ProbabilityGridRangeDataInserter2D>(options_.submaps_options().range_data_inserter_options().probability_grid_range_data_inserter_options_2d());
  //2、计算新插入的点云
  size_t trajectory_nodes_size = trajectory_nodes->SizeOfTrajectoryOrZero(trajectory_id);
  LOG(INFO) << "trajectory_nodes_size:"<<trajectory_nodes_size;
  if (trajectory_nodes_size > 0){

    //pcl::PointCloud<pcl::PointXYZ>::Ptr node_point_cloud(new pcl::PointCloud<pcl::PointXYZ>());
    
    // 遍历轨迹0的所有优化后的节点
    auto node_it = trajectory_nodes->BeginOfTrajectory(trajectory_id);
    auto end_it = trajectory_nodes->EndOfTrajectory(trajectory_id);
    float cosphi;
    float sinphi;
    for (; node_it != end_it; ++node_it) {
      sensor::PointCloud node_point_cloud;
      auto& trajectory_node = trajectory_nodes->at(node_it->id);
      auto& global_pose = trajectory_node.global_pose;
      auto& test = trajectory_node.constant_data->gravity_alignment;
      auto& filtered_gravity_aligned_point_cloud = trajectory_node.constant_data->filtered_gravity_aligned_point_cloud;
      node_point_cloud.empty();
      for (const RangefinderPoint& point :
           filtered_gravity_aligned_point_cloud.points()) {
        double x = test.coeffs().x();
        double y = test.coeffs().y();
        double z = test.coeffs().z();
        double w = test.coeffs().w();
        cosphi = cos(atan2(2*(z*w + x*y),1 - 2*(y*y + z*z)));
        sinphi = sin(atan2(2*(z*w + x*y),1 - 2*(y*y + z*z)));

        double point_x = point.position.x() * cosphi + point.position.y() * sinphi;// + global_pose.translation().x();//point_x = point.position.x() * cosphi + point_x - point.position.y() * sinphi;
        double point_y = -point.position.x() * sinphi + point.position.y() * cosphi;//+ global_pose.translation().y();//point_x = point.position.x() * sinphi + point_x - point.position.y() * sinphi;
        //std::cout << point_x << " " << point.position.x() << std::endl;
        //point.position.x() = point_x;
        //point.position.y() = point_y;
        RangefinderPoint range_finder_point_pre;
        range_finder_point_pre.position.x() = point_x;
        range_finder_point_pre.position.y() = point_y;
        range_finder_point_pre.position.z() = 0;

        RangefinderPoint range_finder_point = global_pose.cast<float>() * range_finder_point_pre;//global_pose.cast<float>() * 

        node_point_cloud.push_back(range_finder_point);
      }
      //LOG(INFO)<<"global_pose.translation().x="<<global_pose.translation().x()<<"  lobal_pose.translation().y="<<global_pose.translation().y();
      sensor::RangeData globle_range_data = sensor::RangeData{{}, {}, {}};

      //globle_range_data.returns = TransformPointCloud(filtered_gravity_aligned_point_cloud, global_pose.cast<float>());
      globle_range_data.returns = node_point_cloud;
      globle_range_data.origin = global_pose.cast<float>().translation();
      
      // 插入地图
      matching_submaps_->InsertRangeData(globle_range_data, range_data_inserter.get());
      
    }
  }
}

/*********************** modify end **************************************************/


}  // namespace mapping
}  // namespace cartographer
