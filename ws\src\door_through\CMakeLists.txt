cmake_minimum_required(VERSION 3.0.2)
project(door_through)

add_compile_options(-std=c++11)

find_package(catkin REQUIRED COMPONENTS roscpp std_msgs geometry_msgs riki_msgs)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  geometry_msgs
  riki_msgs
  nav_msgs
  actionlib
  actionlib_msgs
)

catkin_package(
	INCLUDE_DIRS include
	
)
include_directories(
  include ${catkin_INCLUDE_DIRS}
# include
# ${catkin_INCLUDE_DIRS}
)

add_library(door_through src/door_through.cpp)
add_executable(door_through_node src/door_through_node.cpp)
target_link_libraries(door_through_node door_through ${catkin_LIBRARIES} -pthread -llog4cpp)

install(TARGETS door_through_node door_through
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch
    DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)