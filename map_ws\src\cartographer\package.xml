<?xml version="1.0"?>
<!--
  Copyright 2016 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<package format="3">
  <name>cartographer</name>
  <version>2.0.0</version>
  <description>
    Cartographer is a system that provides real-time simultaneous localization
    and mapping (SLAM) in 2D and 3D across multiple platforms and sensor
    configurations.
  </description>
  <maintainer email="<EMAIL>">
    The Cartographer Authors
  </maintainer>
  <license>Apache 2.0</license>

  <url>https://github.com/cartographer-project/cartographer</url>

  <author email="<EMAIL>">
    The Cartographer Authors
  </author>

  <buildtool_depend>cmake</buildtool_depend>

  <build_depend>git</build_depend>
  <build_depend>google-mock</build_depend>
  <build_depend>gtest</build_depend>
  <build_depend>python3-sphinx</build_depend>

  <depend>libboost-iostreams-dev</depend>
  <depend>eigen</depend>
  <depend>libcairo2-dev</depend>
  <depend>libceres-dev</depend>
  <depend>libgflags-dev</depend>
  <depend>libgoogle-glog-dev</depend>
  <depend>lua5.2-dev</depend>
  <depend>protobuf-dev</depend>

  <export>
    <build_type>cmake</build_type>
  </export>
</package>
