/**
*test.c
*
*
*/
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <alsa/asoundlib.h>
#include <ros/ros.h>
#include <std_msgs/Int32.h>
#include <std_msgs/Int16.h>
#include <riki_msgs/m_voice_ctrl.h>
#include <boost/thread/thread.hpp>


using namespace std;

int voice_flag = 0;
boost::thread* read_thread_;


void speak(string voice_name){
	
	int i;
	int ret;
	int buf[128];
	unsigned int val;
	int dir=0;
	char *buffer;
	int size;
	snd_pcm_uframes_t frames;
	snd_pcm_uframes_t periodsize;
	snd_pcm_t *playback_handle;//PCM设备句柄pcm.h
	snd_pcm_hw_params_t *hw_params;//硬件信息和PCM流配置
	string tmp;
    voice_name = tmp+"/home/<USER>/udrive_v1_1_1/ws/mp3/"+voice_name+".wav";
    FILE *fp = fopen(voice_name.c_str(), "rb");
    if(fp == NULL){
		perror("fp == NULL");
    	return ;
	}
    fseek(fp, 100, SEEK_SET);

    //1. 打开PCM，最后一个参数为0意味着标准配置
    ret = snd_pcm_open(&playback_handle, "default", SND_PCM_STREAM_PLAYBACK, 0);
    if (ret < 0) {
        perror("snd_pcm_open");
        exit(1);
    }

    //2. 分配snd_pcm_hw_params_t结构体
    ret = snd_pcm_hw_params_malloc(&hw_params);
    if (ret < 0) {
        perror("snd_pcm_hw_params_malloc");
        exit(1);
    }
    //3. 初始化hw_params
    ret = snd_pcm_hw_params_any(playback_handle, hw_params);
    if (ret < 0) {
        perror("snd_pcm_hw_params_any");
        exit(1);
    }
    //4. 初始化访问权限
    ret = snd_pcm_hw_params_set_access(playback_handle, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED);
    if (ret < 0) {
        perror("snd_pcm_hw_params_set_access");
        exit(1);
    }
    //5. 初始化采样格式SND_PCM_FORMAT_U8,8位
    ret = snd_pcm_hw_params_set_format(playback_handle, hw_params, SND_PCM_FORMAT_S16_LE);
    if (ret < 0) {
        perror("snd_pcm_hw_params_set_format");
        exit(1);
    }
    //6. 设置采样率，如果硬件不支持我们设置的采样率，将使用最接近的
    //val = 44100,有些录音采样频率固定为8KHz


    val = 44100;
    ret = snd_pcm_hw_params_set_rate_near(playback_handle, hw_params, &val, &dir);
    if (ret < 0) {
        perror("snd_pcm_hw_params_set_rate_near");
        exit(1);
    }
    //7. 设置通道数量  1
    ret = snd_pcm_hw_params_set_channels(playback_handle, hw_params, 1);
    if (ret < 0) {
        perror("snd_pcm_hw_params_set_channels");
        exit(1);
    }

    /* Set period size to 32 frames. */
    frames = 32;
    periodsize = frames ;
    ret = snd_pcm_hw_params_set_buffer_size_near(playback_handle, hw_params, &periodsize);
    if (ret < 0)
    {
         printf("Unable to set buffer size %li : %s\n", frames * 2, snd_strerror(ret));

    }
    periodsize /= 2;

    ret = snd_pcm_hw_params_set_period_size_near(playback_handle, hw_params, &periodsize, 0);
    if (ret < 0)
    {
        printf("Unable to set period size %li : %s\n", periodsize,  snd_strerror(ret));
    }

    //8. 设置hw_params
    ret = snd_pcm_hw_params(playback_handle, hw_params);
    if (ret < 0) {
        perror("snd_pcm_hw_params");
        exit(1);
    }

     /* Use a buffer large enough to hold one period */
    snd_pcm_hw_params_get_period_size(hw_params, &frames, &dir);

    size = frames * 2; /* 2 bytes/sample, 2 channels */
    buffer = (char *) malloc(size);
    fprintf(stderr,
            "size = %d\n",
            size);

    while (voice_flag == 1)
    {
    	
        ret = fread(buffer, 1, size, fp);
		//fprintf(stderr, "2\n");
        if(ret == 0)
        {
              fprintf(stderr, "end of file on input\n");
              break;
        }
        else if (ret != size)
        {
        	fprintf(stderr, "3\n");
        }
		//fprintf(stderr, "4\n");
        //9. 写音频数据到PCM设备
        while((ret = snd_pcm_writei(playback_handle, buffer, frames))<0)
        {
            usleep(2000);
			
            if (ret == -EPIPE)
            {
                  /* EPIPE means underrun */
                  fprintf(stderr, "underrun occurred\n");
                  //完成硬件参数设置，使设备准备好
                  snd_pcm_prepare(playback_handle);
            }
            else if (ret < 0)
            {
                  fprintf(stderr,
                      "error from writei: %s\n",
                      snd_strerror(ret));
            }else{
				printf("snd_pcm_writei %d\n", ret);
            }
        }
		//fprintf(stderr, "5\n");

    }
    
    free(buffer);
    //10. 关闭PCM设备句柄
    snd_pcm_close(playback_handle);
	

}
void readThread()
{
	while(voice_flag == 1){
		speak("disinfect_music_1");
	}
	
}

void play_voice_back(riki_msgs::m_voice_ctrl msg)
{
	// 0 播放语音 1播放背景音乐
	if(msg.type == 1)
	{
		if(msg.cmd==1){	
			printf("播放背景音乐:%s",msg.data);
			if(voice_flag == 0)
			{
				voice_flag = 1;
				read_thread_ = new boost::thread(&readThread);
				
			}
		}else {
			
		}
	}
	
}



void stop_voice_back(riki_msgs::m_voice_ctrl msg)
{
	fprintf(stderr, "stop_voice_back\n");
	voice_flag = 0;
}

int main(int argc, char *argv[])
{
	//while(1)
	ros::init(argc, argv, "alsa_voice_background_node");
	ros::NodeHandle nh;
	ros::Subscriber sub_vel = nh.subscribe("play_voice_ctrl", 10, play_voice_back);
	//speak("disinfect_music_1");
	ros::Subscriber sub_vel_stop = nh.subscribe("stop_voice_ctrl", 10, stop_voice_back);
    ros::spin();
	
    return 0;
}

//注意：编译的时候应该保持“gcc -o test test.c -L. -lasound”的格式，运行的时候应该保持"./test clip2.wav"这种格式。

