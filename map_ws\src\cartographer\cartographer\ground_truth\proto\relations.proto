// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.ground_truth.proto;

import "cartographer/transform/proto/transform.proto";

message Relation {
  int64 timestamp1 = 1;
  int64 timestamp2 = 2;

  // The 'expected' relative transform of the tracking frame from 'timestamp2'
  // to 'timestamp1'.
  transform.proto.Rigid3d expected = 3;
  double covered_distance = 4;
}

message GroundTruth {
  repeated Relation relation = 1;
}
