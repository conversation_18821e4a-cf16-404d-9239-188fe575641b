// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.constraints.proto;

import "cartographer/mapping/proto/scan_matching/ceres_scan_matcher_options_2d.proto";
import "cartographer/mapping/proto/scan_matching/ceres_scan_matcher_options_3d.proto";
import "cartographer/mapping/proto/scan_matching/fast_correlative_scan_matcher_options_2d.proto";
import "cartographer/mapping/proto/scan_matching/fast_correlative_scan_matcher_options_3d.proto";

message ConstraintBuilderOptions {
  // A constraint will be added if the proportion of added constraints to
  // potential constraints drops below this number.
  double sampling_ratio = 1;

  // Threshold for poses to be considered near a submap.
  double max_constraint_distance = 2;

  // Threshold for the scan match score below which a match is not considered.
  // Low scores indicate that the scan and map do not look similar.
  double min_score = 4;

  // Threshold below which global localizations are not trusted.
  double global_localization_min_score = 5;

  // Weight used in the optimization problem for the translational component of
  // loop closure constraints.
  double loop_closure_translation_weight = 13;

  // Weight used in the optimization problem for the rotational component of
  // loop closure constraints.
  double loop_closure_rotation_weight = 14;

  // If enabled, logs information of loop-closing constraints for debugging.
  bool log_matches = 8;

  // Options for the internally used scan matchers.
  mapping.scan_matching.proto.FastCorrelativeScanMatcherOptions2D
      fast_correlative_scan_matcher_options = 9;
  mapping.scan_matching.proto.CeresScanMatcherOptions2D
      ceres_scan_matcher_options = 11;
  mapping.scan_matching.proto.FastCorrelativeScanMatcherOptions3D
      fast_correlative_scan_matcher_options_3d = 10;
  mapping.scan_matching.proto.CeresScanMatcherOptions3D
      ceres_scan_matcher_options_3d = 12;
}
