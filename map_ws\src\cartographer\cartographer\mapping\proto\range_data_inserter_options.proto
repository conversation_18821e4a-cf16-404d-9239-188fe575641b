// Copyright 2018 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/mapping/proto/probability_grid_range_data_inserter_options_2d.proto";
import "cartographer/mapping/proto/tsdf_range_data_inserter_options_2d.proto";

package cartographer.mapping.proto;

message RangeDataInserterOptions {
  enum RangeDataInserterType {
    INVALID_INSERTER = 0;
    PROBABILITY_GRID_INSERTER_2D = 1;
    TSDF_INSERTER_2D = 2;
  }

  RangeDataInserterType range_data_inserter_type = 1;
  ProbabilityGridRangeDataInserterOptions2D
      probability_grid_range_data_inserter_options_2d = 2;
  TSDFRangeDataInserterOptions2D tsdf_range_data_inserter_options_2d = 3;
}
