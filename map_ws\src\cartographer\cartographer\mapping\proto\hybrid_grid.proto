// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

message HybridGrid {
  float resolution = 1;
  // '{x, y, z}_indices[i]' is the index of 'values[i]'.
  repeated sint32 x_indices = 3;
  repeated sint32 y_indices = 4;
  repeated sint32 z_indices = 5;
  // The entries in 'values' should be uint16s, not int32s, but protos don't
  // have a uint16 type.
  repeated int32 values = 6;
}
