/**  
* doctor_mode_node.cpp
*
* 功 能： 消毒模式驱动节点
* 类 名： main()
*
* Ver	 变更日期		  负责人        变更内容
* ────────────────────────────────────────────────────────────────────
* V0.01  2020-04-15    N/A	     初版
*
* Copyright (c) 2020 udrive Corporation. All rights reserved.
*┌────────────────────────────────────────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：南京驭行科技有限公司　　　　　　　　　　　　　　         		            		  │
*└────────────────────────────────────────────────────────────────────┘
*/

#include <iostream>

#include "ros/ros.h"
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <doctor_mode/doctor_mode.h>


int main(int argc, char **argv)  
{
	ros::init(argc, argv, "doctor_mode_node");
	ros::NodeHandle n;	  
	DoctorMode doctorMode;
	ros::spin();
	return 0;
}



