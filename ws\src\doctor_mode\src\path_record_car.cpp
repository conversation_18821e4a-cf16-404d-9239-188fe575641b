/**********************************************************************************
 *  文件名：      path_record.cpp
 *  版权：        南京驭行科技有限公司
 *  描述：        路径录制程序
 *  修改人：       何世政
 *  修改时间：      2020-11-30
 *  修改内容：      修改订阅路径方式，以免地图被修正导致路径和实际位置不匹配
 **********************************************************************************/
#include <iostream>
#include <mutex>

#include <ros/ros.h>
#include <log4cpp/PropertyConfigurator.hh>
#include <log4cpp/Category.hh>
#include <doctor_mode/doctor_mode.h>
#include <riki_msgs/yx_path_control.h>
#include <nav_msgs/GetPlan.h>
#include <nav_msgs/Path.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/PoseStamped.h>
#include <visualization_msgs/MarkerArray.h>

std::mutex mtx;
nav_msgs::Path path_;

void robot_pose_call_back(const visualization_msgs::MarkerArray &robot_pose_)
{
	mtx.lock();
	
	geometry_msgs::PoseStamped pose_tmp;
	pose_tmp.header.stamp = ros::Time::now();
	pose_tmp.header.frame_id = "map";
	
	path_.poses.clear();
	
	for(int i=0;i<robot_pose_.markers.size();i++)
	{
		for(auto it = robot_pose_.markers[i].points.begin();it!= robot_pose_.markers[i].points.end();++it)
		{
			pose_tmp.pose.position.x = it->x;	
			pose_tmp.pose.position.y = it->y;	
			pose_tmp.pose.position.z = it->z;
			pose_tmp.pose.orientation.x = 0;	
			pose_tmp.pose.orientation.y = 0;	
			pose_tmp.pose.orientation.z = 0;	
			pose_tmp.pose.orientation.w = 1;	
			path_.poses.push_back(pose_tmp);	
		}
	}
	
	//log.info("修剪完路径点数：%d",path_.poses.size());
	
	//log.info("收到机器人位姿数据");
	mtx.unlock();
	
}


int main(int argc, char** argv){
	ros::init(argc, argv, "path_record");
	
	ros::NodeHandle  nh;
	
	ros::Subscriber  rosbot_pose_sub_ = nh.subscribe("trajectory_node_list", 10, robot_pose_call_back);
	ros::Publisher path_record_pub_ = nh.advertise<const nav_msgs::Path>("path_record", 10);
	
	// 读取解析配置文件基础路径
	string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	
    // 读取解析配置文件
    try
    {
        log4cpp::PropertyConfigurator::configure(config_base_path+"/doctor.conf");
    }
    catch (log4cpp::ConfigureFailure& f)
    {
        std::cout << "Configure Problem: " << f.what() << std::endl;
    }
	
	log4cpp::Category& log = log4cpp::Category::getRoot(); //日志句柄
	
	sleep(3);
		
	ros::Rate loop_rate(1);
    while(ros::ok())
    {
    	ros::spinOnce();
    	mtx.lock();
    	nav_msgs::Path path_send_;
    	
    	//按距离裁剪路径
    	double distance = 0;
		double last_distance = 0;
		geometry_msgs::PoseStamped pose_tmp;
		geometry_msgs::PoseStamped last_pose_tmp;
    	for(int i=0;i<path_.poses.size();i++)
		{
			pose_tmp = path_.poses[i];
			if(i == 0 || i == path_.poses.size()-1)
			{
				last_pose_tmp = pose_tmp;
				last_distance = 0;
				
				path_send_.poses.push_back(pose_tmp);
			}else
			{
				distance = hypot(last_pose_tmp.pose.position.x-pose_tmp.pose.position.x,
											last_pose_tmp.pose.position.y-pose_tmp.pose.position.y);
											
				if(distance > 0.2)		
				{
					pose_tmp= path_.poses[i];
					path_send_.poses.push_back(pose_tmp);
					
					last_pose_tmp = pose_tmp;
					last_distance = 0;

				}else
				{												
					last_distance = distance;
				}	
			}
		}
		
		log.info("修剪完路径点数：%d",path_send_.poses.size());
		path_record_pub_.publish(path_send_);	
		mtx.unlock();
		
		loop_rate.sleep();
	}
	
	ros::spin();
	return 0;
}

