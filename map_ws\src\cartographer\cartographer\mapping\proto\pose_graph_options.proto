// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/mapping/proto/pose_graph/constraint_builder_options.proto";
import "cartographer/mapping/proto/pose_graph/optimization_problem_options.proto";

message PoseGraphOptions {
  // Online loop closure: If positive, will run the loop closure while the map
  // is built.
  int32 optimize_every_n_nodes = 1;

  // Options for the constraint builder.
  mapping.constraints.proto.ConstraintBuilderOptions
      constraint_builder_options = 3;

  // Weight used in the optimization problem for the translational component of
  // non-loop-closure scan matcher constraints.
  double matcher_translation_weight = 7;

  // Weight used in the optimization problem for the rotational component of
  // non-loop-closure scan matcher constraints.
  double matcher_rotation_weight = 8;

  // Options for the optimization problem.
  mapping.optimization.proto.OptimizationProblemOptions
      optimization_problem_options = 4;

  // Number of iterations to use in 'optimization_problem_options' for the final
  // optimization.
  int32 max_num_final_iterations = 6;

  // Rate at which we sample a single trajectory's nodes for global
  // localization.
  double global_sampling_ratio = 5;

  // Whether to output histograms for the pose residuals.
  bool log_residual_histograms = 9;

  // If for the duration specified by this option no global contraint has been
  // added between two trajectories, loop closure searches will be performed
  // globally rather than in a smaller search window.
  double global_constraint_search_after_n_seconds = 10;

  message OverlappingSubmapsTrimmerOptions2D {
    int32 fresh_submaps_count = 1;
    double min_covered_area = 2;
    int32 min_added_submaps_count = 3;
  }

  // Instantiates the 'OverlappingSubmapsTrimmer2d' which trims submaps from the
  // pose graph based on the area of overlap.
  OverlappingSubmapsTrimmerOptions2D overlapping_submaps_trimmer_2d = 11;
}
