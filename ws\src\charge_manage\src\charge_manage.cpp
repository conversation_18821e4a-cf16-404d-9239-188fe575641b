﻿/**********************************************************************************
 *  锟侥硷拷锟斤拷锟斤拷       charge_manage.cpp
 *  锟斤拷权锟斤拷         锟较撅拷驭锟斤拷萍锟斤拷锟斤拷薰锟剿�
 *  锟斤拷锟斤拷锟斤拷         锟斤拷锟皆讹拷锟斤拷锟斤拷锟斤拷一锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷要锟斤拷锟杰ｏ拷
						 1 锟斤拷锟斤拷锟斤拷锟斤拷锟狡碉拷锟斤拷锟阶�锟斤拷时锟斤拷锟津开筹拷锟酵�   (锟叫讹拷锟斤拷锟斤拷锟斤拷锟斤拷锟酵凤拷锟斤拷藕牛锟斤拷锟斤拷锟斤拷泻锟斤拷锟斤拷藕锟�)
						 2 锟斤拷锟斤拷锟剿憋拷锟斤拷锟斤拷锟斤拷桩锟斤拷锟截闭筹拷锟酵� 锟斤拷锟叫讹拷锟斤拷锟斤拷锟斤拷 锟斤拷锟斤拷没锟脚号伙拷锟竭伙拷锟斤拷锟斤拷锟狡讹拷锟斤拷一锟轿撅拷锟诫）
						 3 锟斤拷锟斤拷锟斤拷锟叫ｏ拷锟斤拷锟斤拷锟斤拷锟斤拷95时,锟截闭筹拷锟酵凤拷锟饺伙拷锟斤拷锟斤拷80%锟斤拷锟津开筹拷锟酵�
						 
 *  锟斤拷锟戒：			 1 锟斤拷锟接硷拷獾斤拷锟斤拷桩锟斤拷锟斤拷  get_dock
 **********************************************************************************/
#include <ros/ros.h>
#include <iostream>
#include <string>
#include <stdlib.h>

#include "charge_manage/charge_manage.h"

ChargeManage::ChargeManage():move_distance_threshold(0.05),
                            low_current_threshold(85.0),
									 high_current_threshold(95.0),
									 use_infrared(false),
									 use_emergency(false),
									 battery_current(-1.0),
									 battery_percentage(100),
									 battery_current_threshold(-0.9),
									 confirm_over_current_threshold(false),
									 charge_state(0),
									 get_odom_msg(false),
									 manual_close_charge(false),
									 auto_charing_state(0),
									 first_current(false),
									 first_move(false)
{
	ros::NodeHandle nh;
	ros::NodeHandle np("~");
    
	// 锟斤拷取锟斤拷锟斤拷锟斤拷锟斤拷锟侥硷拷锟斤拷锟斤拷路锟斤拷
	std::string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	// 锟斤拷取锟斤拷锟斤拷锟斤拷锟斤拷锟侥硷拷
	try
	{
		log4cpp::PropertyConfigurator::configure(config_base_path+"/charge_manage.conf");
	}
	catch (log4cpp::ConfigureFailure& f)
	{
		std::cout << "Configure Problem: " << f.what() << std::endl;
	}
	
	charge_switch_topic = "system_charge_state";
	infrared_topic = "infrared_status";
	odom_topic = "odom";
	battery_state_topic = "battery_state";
	emergency_stop_topic = "stop_btn";
	dock_state_topic = "docking_state";
	
	charge_control_pub_ = nh.advertise<std_msgs::Bool>("charge_control", 1);
	motor_control_pub_ = nh.advertise<std_msgs::Bool>("motor_control", 1);
	dock_pub_ = nh.advertise<riki_msgs::robot_auto_dock_state>("auto_dock_state", 1);
	cmd_vel_pub_ = nh.advertise<geometry_msgs::Twist>("cmd_vel", 1);
	
	get_dock_pub_ = nh.advertise<std_msgs::Bool>("get_dock", 1);
	
	battery_state_sub_ = nh.subscribe(battery_state_topic, 1, &ChargeManage::battery_state_call_back,this);
	auto_dock_sub_ = nh.subscribe(dock_state_topic, 1, &ChargeManage::dock_state_call_back,this);
	
	np.param("move_distance_threshold", move_distance_threshold, 0.05);
	np.param("low_current_threshold", low_current_threshold, 85.0);
	np.param("high_current_threshold", high_current_threshold, 95.0);
	np.param("battery_current_threshold", battery_current_threshold, -0.9);
	np.param("positive_charge_threshold", positive_charge_threshold, 2.0);
	np.param("negative_charge_threshold", negative_charge_threshold, -1.0);

	np.param("referenc_voltage", referenc_voltage, 29.4);
	np.param("referenc_current", referenc_current, -0.2);
	
	np.param("use_voltage", use_voltage, false);
	np.param("use_infrared", use_infrared, false);
	np.param("use_emergency", use_emergency, false);
	np.param("use_current", use_current, true);
	np.param("use_odom", use_odom, false);
	
	log.info("move_distance_threshold: %f    battery_current_threshold: %f ",move_distance_threshold,battery_current_threshold);
	log.info("low_current_threshold:   %f    high_current_threshold: %f",low_current_threshold,high_current_threshold);
	log.info("use_current:   %d    use_odom: %d",use_current,use_odom);
	

	ros::Duration(3.0).sleep();
	log.info("charge manage initialization completed");
}

ChargeManage::~ChargeManage()
{ 
	
}

void ChargeManage::dock_state_call_back(const std_msgs::UInt8 & msg)
{
	auto_charing_state = msg.data;
}

void ChargeManage::battery_state_call_back(const sensor_msgs::BatteryState & msg)
{
	battery_current = msg.current;
	battery_percentage = msg.percentage;
	battery_voltage = msg.voltage;
	
	log.info("battery_current %f      battery_percentage %f   battery_voltage %f ",battery_current,battery_percentage,battery_voltage);
	
	
	
}

bool ChargeManage::move_threshold_exceeded(double &x1, double &y1, double &x2, double &y2, double &move_threshold )
{
	double dis =  sqrt((x1-x2)*(x1-x2) + (y1-y2)*(y1-y2));
	
	if(dis > move_threshold)
		return true;
	else
		return false;	
}

bool ChargeManage::move_threshold_exceeded(Point& p1, Point& p2, double &move_threshold )
{
	double dis =  sqrt((p1.x-p2.x)*(p1.x-p2.x) + (p1.y-p2.y)*(p1.y-p2.y));
	
	if(dis > move_threshold)
		return true;
	else
		return false;
}

bool ChargeManage::get_system_charge_state()
{
	// 抓取锟斤拷锟斤拷头锟脚猴拷
	boost::shared_ptr<std_msgs::Bool const> system_charge_state_;
	system_charge_state_ = ros::topic::waitForMessage<std_msgs::Bool>(charge_switch_topic,  ros::Duration(5));
	
	if(system_charge_state_!= NULL)
	{
		if(system_charge_state_->data)
		{
			log.info("Voltage signal detected, system_charge is true");
			return true;
		}else{
			log.info("Voltage signal detected, system_charge is false");
			return false;
		}
	}else
	{
		log.error("No voltage signal detected.");
		return false;
	}
}

bool ChargeManage::get_infrared_state()
{
	// 抓取锟斤拷锟斤拷锟脚猴拷
	boost::shared_ptr<riki_msgs::infrared_status const> infrared_status_;
	infrared_status_ = ros::topic::waitForMessage<riki_msgs::infrared_status>(infrared_topic,  ros::Duration(5));
	
	if(infrared_status_ != NULL && (infrared_status_->left_center!=0|| infrared_status_->right_center!=0)) 
	{
		log.info("Infrared signal detected.");
		return true;
	}else
	{
		log.error("No infrared signal detected.");
		return false;
	} 
				
}

bool ChargeManage::get_emergency_state()
{
	// 抓取锟斤拷停锟脚猴拷
	boost::shared_ptr<std_msgs::Bool const> stop_btn_state_;
	stop_btn_state_ = ros::topic::waitForMessage<std_msgs::Bool>(emergency_stop_topic,  ros::Duration(5));
	
	if(stop_btn_state_ != NULL) 
	{
		if(stop_btn_state_->data)
		{
			//log.info("Emergency signal detected, stop_btn_state_ is true");
			return false;
		}else{
			//log.info("Emergency signal detected, stop_btn_state_ is false");
			return true;
		}
	}else
	{
		log.error("No emergency stop signal detected.");
		return false;
	} 
}

bool ChargeManage::get_odom(Point& p)
{
	// 锟斤拷取锟斤拷前位锟矫碉拷锟斤拷碳锟斤拷锟斤拷锟�
	boost::shared_ptr<nav_msgs::Odometry const> odom_;
	odom_ = ros::topic::waitForMessage<nav_msgs::Odometry>(odom_topic,  ros::Duration(2));
	if(odom_!= NULL)  
	{
		p.x = odom_->pose.pose.position.x;
		p.y = odom_->pose.pose.position.y;
	}
	else
	{
		return false;
	}
}

bool ChargeManage::battery_charge_manage()
{
	int use_current_cout = 0;
	int close_charge_touch_cont =0;//
	
	ros::Rate loop_rate(1);
	while(ros::ok())
	{
		if(auto_charing_state ==5)
		{
			
			charge_state = 0;
			auto_charing_state = 0;
			log.info("After receiving the command of automatic charging program, end the charging state");
			
		}
		
		switch(charge_state){
			case 0://闈炲厖鐢电姸鎬�
			{
				
				riki_msgs::robot_auto_dock_state auto_dock_state_;
				auto_dock_state_.type = 0;
				auto_dock_state_.error = 0;
				auto_dock_state_.working = false;
				auto_dock_state_.charged = false;
				auto_dock_state_.total_time = 0.0; 
				auto_dock_state_.try_times = 0;
				dock_pub_.publish(auto_dock_state_);


				bool emergency_state_flag = get_emergency_state();//妫€娴嬫€ュ仠淇″彿
				bool charge_flag = get_system_charge_state();//妫€娴嬫槸鍚︽湁鍏呯數妗╂帴瑙︿俊鍙�
				//鏈夊厖鐢垫々鎺ヨЕ淇″彿
				if(emergency_state_flag)
				{	
					if(charge_flag){
						//manual_close_charge = false;
						std_msgs::Bool msg;
						msg.data = true;
						charge_control_pub_.publish(msg);	   //鎵撳紑鐢垫睜鍏呯數缁х數鍣�						
					}
				}
				
				//妫€鏌ユ槸鍚﹀嚭鐜颁簡鍏呯數鐢垫祦
				if(battery_current > referenc_current  )
				{					
					if(start_current_threshold_times >=2)
					{
						//杩炵画3娆℃�€娴嬪埌鍏呯數鐢垫祦
						start_current_threshold_times = 0;
						if(charge_flag)//妫€娴嬪埌鍏呯數妗�
						{
							
							if(emergency_state_flag){
								 std_msgs::Bool msg;
								 msg.data = true;
								 
								 if(auto_charing_state == 1 ||	auto_charing_state == 2)
								 {
									charge_state = 2;//鑷�鍔ㄨ繑鍥炲厖鐢垫々
								 }
								 else{
								 	motor_control_pub_.publish(msg);	   //鐢垫満浣胯兘
									charge_state = 3;
								}//鎺ㄥ埌鍏呯數妗�
							}else
							{
								//鎬ュ仠瑙﹀彂锛屼笉杩涘叆鍏呯數鐘舵€�
							}		
						}
						else//娌℃湁妫€娴嬪埌鍏呯數妗╀俊鍙�
						{	
							//杩涘叆鎵嬪姩鍏呯數鐘舵€�
							charge_state = 1;
												  
						}																		
					}else{
						start_current_threshold_times++;
					}
					
				}else{
					//娌℃湁妫€娴嬪埌鍏呯數鐢垫祦
					start_current_threshold_times = 0;					
					
					//娌℃湁鐢垫祦鐨勬儏鍐典笅鏈夊厖鐢垫々淇″彿澶勭悊
					if(charge_flag){
						//鏈夌數鍘嬩俊鍙凤紝浣嗘槸娌℃湁鐢垫祦
						if(emergency_state_flag){//鎬ュ仠娌℃湁琚�鎸変笅璇存槑宸茬粡鑴辩�诲厖鐢垫々锛岀數姹犳病鏈夋柇寮€
						                         //鏈�鑴辩�诲厖鐢垫々锛屼絾鏄�娌℃湁鐢垫祦
							close_charge_touch_cont++;
							if(close_charge_touch_cont>10){//杩炵画妫€娴�10娆＄數姹犵數娴侊紝濡傛灉鐢垫祦涓€鐩翠綆浜庨槇鍊�
								close_charge_touch_cont = 0;
								//宸茬粡鑴辩�诲厖鐢垫々浜嗭紝鐢垫睜缁х數鍣ㄨ繕澶勪簬鎵撳紑鐘舵€�
								std_msgs::Bool msg;
								msg.data = false;
								//charge_control_pub_.publish(msg);	   //鍏抽棴鐢垫睜鍏呯數缁х數鍣�	锛屼复鏃跺叧闂�
							}
						}else{//鎬ュ仠琚�鎸変笅浜�
							//浠€涔堥兘涓嶅仛
						}
					}
					
				}
				
				break;
				
			}
			case 1:   // 鎵嬪姩鍏呯數鐘舵€�
			{
				riki_msgs::robot_auto_dock_state auto_dock_state_;
				auto_dock_state_.type = charge_state;
				auto_dock_state_.error = 0;
				auto_dock_state_.working = false;
				auto_dock_state_.charged = true;
				auto_dock_state_.total_time = 0.0; 
				auto_dock_state_.try_times = 0;
				dock_pub_.publish(auto_dock_state_);
				//妫€鏌ュ厖鐢电數娴佹槸鍚︾粨鏉�
				if(battery_current <= referenc_current  )
				{
					if(end_current_threshold_times >=3)
					{
						charge_state = 0;
						auto_charing_state = 0;
					}else{
						end_current_threshold_times++;
					}
				}else{
					end_current_threshold_times = 0;
				}
				
				break;
				
			}
			case 2:
			case 3:
			{
				riki_msgs::robot_auto_dock_state auto_dock_state_;
				auto_dock_state_.type = charge_state;
				auto_dock_state_.error = 0;
				auto_dock_state_.working = false;
				auto_dock_state_.charged = true;
				auto_dock_state_.total_time = 0.0; 
				auto_dock_state_.try_times = 0;
				dock_pub_.publish(auto_dock_state_);
//				bool charge_flag = get_system_charge_state();//妫€娴嬫槸鍚︽湁鍏呯數妗╂帴瑙︿俊鍙�
//				if(!charge_flag){
//					std_msgs::Bool msg;
//					msg.data = false;
//					charge_control_pub_.publish(msg);
//
//					charge_state = 0;
//					auto_charing_state = 0;
//					break;
//				}
				bool charge_flag ;
				if(use_emergency)
				{
					charge_flag = get_emergency_state();
					if(!charge_flag)
					{
						std_msgs::Bool msg;
						msg.data = false;
						charge_control_pub_.publish(msg);

						charge_state = 0;
						auto_charing_state = 0;
						//confirm_over_current_threshold = false;
						log.info("End off charging state");
						
						std_msgs::Bool msg_dock;
						msg_dock.data = false;
						get_dock_pub_.publish(msg_dock);
						
						break;
					}
				}
				
				//if(use_current && !manual_close_charge)
				if(use_current)	{		
					if(battery_current < referenc_current)
					{
						if(use_current_cout>3){
							use_current_cout = 0;
							std_msgs::Bool msg;
							msg.data = false;
							charge_control_pub_.publish(msg);

							charge_state = 0;
							auto_charing_state = 0;
							log.info("current change, close charge switch");
							log.info("now current %f, referenc_current %f",battery_current,referenc_current);
														
							break;			
						}else{
							use_current_cout++;
						}
					}
					else{
						use_current_cout = 0;
					}
				}
				break;			
			}
			default:
			{
				break;
				
			}
		}	
		loop_rate.sleep();
	}
	
}
