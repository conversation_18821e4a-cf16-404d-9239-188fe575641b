<launch>
    <!--output="screen"-->
	<node name="charge_manage_node" pkg="charge_manage" type="charge_manage_node" output="screen">
		<param name="move_distance_threshold" value="0.05"/>
		<param name="low_current_threshold" value="90.0"/>
		<param name="high_current_threshold" value="100.0"/>
		
		<param name="positive_charge_threshold" value="2.0"/>
		<param name="negative_charge_threshold" value="-1.0"/>
		<param name="referenc_voltage" value="29.4"/>
		<param name="use_voltage" value="false"/>  <!--不开启，充电时电池电压和充电器电压不一致-->
		<param name="use_infrared" value="false"/>
		<param name="use_emergency" value="true"/>
		<param name="use_current" value="true"/>		
		<param name="use_odom" value="false"/>
	</node>
</launch>
