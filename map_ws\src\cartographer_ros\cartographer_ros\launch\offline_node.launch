<!--
  Copyright 2018 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<launch>
  <arg name="bag_filenames"/>
  <arg name="no_rviz"/>
  <arg name="rviz_config"/>
  <arg name="configuration_directory"/>
  <arg name="configuration_basenames"/>
  <arg name="urdf_filenames"/>
  <arg name="launch_prefix"/>

  <param name="/use_sim_time" value="true" />

  <group unless="$(arg no_rviz)">
    <node name="rviz"
          pkg="rviz"
          type="rviz"
          required="true"
          args="-d $(arg rviz_config)" />

    <node name="cartographer_occupancy_grid_node"
        pkg="cartographer_ros"
        type="cartographer_occupancy_grid_node"
        args="-resolution 0.05" />
  </group>

  <node name="cartographer_offline_node" pkg="cartographer_ros"
      required="$(arg no_rviz)"
      type="cartographer_offline_node" args="
          -configuration_directory $(arg configuration_directory)
          -configuration_basenames $(arg configuration_basenames)
          -urdf_filenames $(arg urdf_filenames)
          -bag_filenames $(arg bag_filenames)"
      launch-prefix="$(arg launch_prefix)"
      output="screen"/>
</launch>
