cmake_minimum_required(VERSION 2.8.3)
project(doctor_mode)

add_compile_options(-std=c++11)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  riki_msgs
  actionlib
  actionlib_msgs
)


catkin_package(
  INCLUDE_DIRS include
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

add_library(doctor_mode src/doctor_mode.cpp)
add_executable(doctor_mode_node src/doctor_mode_node.cpp)
target_link_libraries(doctor_mode_node doctor_mode ${catkin_LIBRARIES} -pthread -llog4cpp)

add_executable(doctor_mode_node_test src/doctor_mode_node_test.cpp)
target_link_libraries(doctor_mode_node_test doctor_mode ${catkin_LIBRARIES} -pthread -llog4cpp)

add_executable(path_record src/path_record.cpp)
target_link_libraries(path_record ${catkin_LIBRARIES} -pthread -llog4cpp)

add_executable(path_record_car src/path_record_car.cpp)
target_link_libraries(path_record_car ${catkin_LIBRARIES} -pthread -llog4cpp)


## Mark executables and/or libraries for installation
install(TARGETS doctor_mode_node  doctor_mode doctor_mode_node_test path_record path_record_car
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## Mark all other required files for installation

install(DIRECTORY launch
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)
