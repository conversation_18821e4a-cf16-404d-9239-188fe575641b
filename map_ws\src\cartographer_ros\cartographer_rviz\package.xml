<?xml version="1.0"?>
<!--
  Copyright 2016 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<package format="2">
  <name>cartographer_rviz</name>
  <version>1.0.0</version>
  <description>
    Cartographer is a system that provides real-time simultaneous localization
    and mapping (SLAM) in 2D and 3D across multiple platforms and sensor
    configurations. This package provides <PERSON><PERSON><PERSON>'s RViz integration.
  </description>
  <maintainer email="<EMAIL>">
    The Cartographer Authors
  </maintainer>
  <license>Apache 2.0</license>

  <url>https://github.com/cartographer-project/cartographer_ros</url>

  <author email="<EMAIL>">
    The Cartographer Authors
  </author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>git</build_depend>

  <depend version_gte="1.0.0">cartographer</depend>
  <depend version_gte="1.0.0">cartographer_ros</depend>
  <depend version_gte="1.0.0">cartographer_ros_msgs</depend>
  <depend>libqt5-core</depend>
  <depend>libqt5-gui</depend>
  <depend>libqt5-widgets</depend>
  <depend>message_runtime</depend>
  <depend>qtbase5-dev</depend>
  <depend>roscpp</depend>
  <depend>roslib</depend>
  <depend>rviz</depend>

  <export>
      <rviz plugin="${prefix}/rviz_plugin_description.xml" />
  </export>
</package>
