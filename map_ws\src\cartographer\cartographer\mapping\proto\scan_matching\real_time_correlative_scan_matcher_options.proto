// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.scan_matching.proto;

message RealTimeCorrelativeScanMatcherOptions {
  // Minimum linear search window in which the best possible scan alignment
  // will be found.
  double linear_search_window = 1;

  // Minimum angular search window in which the best possible scan alignment
  // will be found.
  double angular_search_window = 2;

  // Weights applied to each part of the score.
  double translation_delta_cost_weight = 3;
  double rotation_delta_cost_weight = 4;
}
