#-------定义rootCategory的属性-------

#指定rootCategory的log优先级是ERROR，其Appenders有两个，分别是console,TESTAppender
log4cpp.rootCategory=DEBUG,TESTAppender,console

#-------定义console属性-------

#consoleAppender类型:控制台输出
#下面这三条语句表示控制台输出的log输出的布局按照指定的格式；输出格式是：[%p] %d{%H:%M:%S.%l} (%c): %m%n
log4cpp.appender.console=ConsoleAppender
log4cpp.appender.console.layout=PatternLayout
log4cpp.appender.console.layout.ConversionPattern=[%p] %d{%H:%M:%S.%l} (%c): %m%n

#-------定义TESTAppender的属性-------

#RollingFileAppender类型：输出到回卷文件，即文件到达某个大小的时候产生一个新的文件
#下面的语句表示文件输出到指定的log文件，输出的布局按照指定的格式，输出的格式是：[%d{%Y-%m-%d %H:%M:%S.%l} - %p] (%c): %m%n
log4cpp.appender.TESTAppender=RollingFileAppender

#当日志文件到达maxFileSize大小时，将会自动滚动
log4cpp.appender.TESTAppender.maxFileSize=10000000

#maxBackupIndex指定可以产生的滚动文件的最大数
log4cpp.appender.TESTAppender.maxBackupIndex=10

#fileName指定信息输出到logs/TESTAppender.txt文件
log4cpp.appender.TESTAppender.fileName=/home/<USER>/udrive_v1_1_1/ws/log/elevator_collaboration.log

#PatternLayout 表示可以灵活指定布局模式
log4cpp.appender.TESTAppender.layout=PatternLayout

#append=true 信息追加到上面指定的日志文件中，false表示将信息覆盖指定文件内容
log4cpp.appender.TESTAppender.append=true
log4cpp.appender.TESTAppender.layout.ConversionPattern=[%d{%Y-%m-%d %H:%M:%S.%l} - %p] (%c): %m%n