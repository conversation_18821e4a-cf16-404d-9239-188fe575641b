/**********************************************************************************
 *  文件名：       door_through.cpp
 *  版权：        南京驭行科技有限公司
 *  描述：         门禁任务模式
 *  修改人：       何世政
 *  修改时间：      2021-4-12
 *  修改内容：      新增
 */
#include <door_through/door_through.h>

//产生a到b之间的数， 不包括a 和 b
#define random(a,b) (rand()%(b-a)+a)

/**
 * @说明	 构造函数
 */
DoorThrough::DoorThrough():ac("move_base", true)
{
   ros::NodeHandle private_nh("~");
	ros::NodeHandle nh;

	// 读取解析配置文件基础路径
	std::string config_base_path = getenv("CONFIG_BASE_PATH");
	std::cout <<config_base_path << std::endl;
	// 读取解析配置文件
	try
	{
	    log4cpp::PropertyConfigurator::configure(config_base_path+"/door_through.conf");
	}
	catch (log4cpp::ConfigureFailure& f)
	{
	    std::cout << "Configure Problem: " << f.what() << std::endl;
	}
	
	// 注册服务original_path_srv_
	original_path_srv_ = nh.advertiseService("original_path", &DoorThrough::original_path_callback, this);
	
    //清地图 获取路径
	map_clear_client = private_nh.serviceClient<std_srvs::Empty>("/move_base/clear_costmaps");
	make_plan_client_ = private_nh.serviceClient<nav_msgs::GetPlan>("/move_base/make_plan");
    
    //门禁控制
    access_control_pub_ = nh.advertise<std_msgs::UInt8>("access_ctrl", 1);

    //过门状态
    door_through_pub_ = nh.advertise<std_msgs::Bool>("door_through", 1);

    //电机控制
    motor_control_pub_ = nh.advertise<std_msgs::Bool>("motor_control", 1);

    //调试开关
    debug_ = true;

    door_through_state =0;

    if(debug_)
    {
        path_pub_ = nh.advertise<nav_msgs::Path>("original_path_visual", 1);
    }
    

    log.info("DoorThrough 初始化完成!");
}

/**
 * @说明	 析构函数
 */
DoorThrough::~DoorThrough()
{

}

/**
 * @说明	 路径回调函数
 */

 bool DoorThrough::original_path_callback(riki_msgs::yx_door_control::Request  &req,riki_msgs::yx_door_control::Response &res)
 {
    ros::WallTime start = ros::WallTime::now();
    if(req.cmd==0)
    {
        //第一步 将障碍物层关闭
        system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'false' ");
        //system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer1 enabled 'false' ");
        //sleep(1);

        //第二步 获取机器人当前位置
        sharePose = ros::topic::waitForMessage<geometry_msgs::Pose>("robot_pose",  ros::Duration(3));
        geometry_msgs::Pose share_current_pose;
        if(sharePose != NULL)
        {	
            share_current_pose = *sharePose;
        }else{
            log.debug("获取不到机器人位置");
            res.ret = -2;
            system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'true' ");
            return true;
        }
        
        //第三步 调用服务规划路径
        geometry_msgs::PoseStamped start_pose, end_pose;
        start_pose.header.frame_id = "map";
        start_pose.header.stamp = ros::Time::now();		
        start_pose.pose = share_current_pose;
                
        end_pose.header.frame_id = "map";
        end_pose.header.stamp = ros::Time::now();		
        end_pose.pose = req.goal_pose_info;
        
        ROS_INFO_STREAM(start_pose);
        ROS_INFO_STREAM(end_pose);
        
        nav_msgs::GetPlan get_plan_srv;
        get_plan_srv.request.start = start_pose;
        get_plan_srv.request.goal = end_pose;
        
        log.debug("start: %.9f\n  %.9f\n   goal: %.9f\n   %.9f\n",start_pose.pose.position.x,start_pose.pose.position.y,end_pose.pose.position.x,end_pose.pose.position.y);

		  for(int i=0;i<10;i++)
		  {
				if(make_plan_client_.call(get_plan_srv))
			  {
					res.ret = get_plan_srv.response.plan.poses.size();
					
			  }else{
					log.debug("规划路径失败");
					res.ret = -3;
					system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'true' ");
					//system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer1 enabled 'true' ");
					return true;
			  }
			  
			  log.info("num of plan: %d",res.ret);
			  
			  if(get_plan_srv.response.plan.poses.size()>0)
			  {
					break;
			  }else if(i==9)
			  {
					log.debug("计算路径彻底失败");
					res.ret = -3;
					system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'true' ");
					//system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer1 enabled 'true' ");
					return true;
			  }else
			  {
					//get_plan_srv.response.plan.clear();
					log.info("路径计算失败");
			  }
				
		}
        
        

        if(debug_)
        {
				nav_msgs::Path path_tmp;
				log.info("num of door_access: %d",req.door_access.size());
				for(int i=0;i<res.ret;i++)
				{
					geometry_msgs::PoseStamped pose_tmp =  get_plan_srv.response.plan.poses[i];
					
					for(int j=0;j<req.door_access.size();j++)
					{
						if(hypot(pose_tmp.pose.position.x-req.door_access[j].x,pose_tmp.pose.position.y-req.door_access[j].y)<=req.door_access[j].z)
						{
							if(path_tmp.poses.size()==0)
							{
								path_tmp.poses.push_back(pose_tmp);
							
							}else
							{
								if(hypot(path_tmp.poses.back().pose.position.x-pose_tmp.pose.position.x,path_tmp.poses.back().pose.position.y-pose_tmp.pose.position.y)>=0.2)
								{
									path_tmp.poses.push_back(pose_tmp);
									//log.debug("门禁附近，添加到路径中");
								}else
								{
									//log.debug("门禁附近，但路径过于密集，跳过");
								}
							}
							break;
						}
					}
				}
				
				
            nav_msgs::Path path_msg;
            
            path_msg = path_tmp;
            path_msg.header.stamp = ros::Time::now();		
            path_msg.header.frame_id = "map";
            log.info("num of filter_plan: %d",path_msg.poses.size());
            
            //ROS_INFO_STREAM(path_msg.poses);

            path_pub_.publish(path_msg);
        }

        //第五步 将障碍物层打开
        system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'true' ");
        //system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer1 enabled 'true' ");
        
        ros::WallDuration t_diff = ros::WallTime::now() - start;
			log.debug("---------original_path_srv cost time: %.9f\n", t_diff.toSec());
    
			return true;
        
    }else if(req.cmd==1)
    {   
    	  ac.cancelGoal();
    	  sleep(1);
        
       sharePose = ros::topic::waitForMessage<geometry_msgs::Pose>("robot_pose",  ros::Duration(3));
        geometry_msgs::Pose share_current_pose;
        if(sharePose != NULL)
        {	
            share_current_pose = *sharePose;
        }else{
            log.debug("获取不到机器人位置");
            res.ret = -2;
            system("rosrun dynamic_reconfigure dynparam set /move_base/global_costmap/obstacle_layer enabled 'true' ");
            return true;
        }
        
        //
        geometry_msgs::PoseStamped start_pose, end_pose;
        start_pose.header.frame_id = "map";
        start_pose.header.stamp = ros::Time::now();		
        start_pose.pose = share_current_pose;
                
        end_pose.header.frame_id = "map";
        end_pose.header.stamp = ros::Time::now();		
        end_pose.pose = req.goal_pose_info;      
        
        log.debug("start: %.9f\n   %.9f\n  goal: %.9f\n   %.9f\n",start_pose.pose.position.x,start_pose.pose.position.y,end_pose.pose.position.x,end_pose.pose.position.y);

        double reference_distance = hypot(start_pose.pose.position.x-end_pose.pose.position.x,
                                          start_pose.pose.position.y-end_pose.pose.position.y);
        log.debug("reference_distance: %.9f\n",reference_distance);
        
        std_msgs::UInt8 door_msg;
        //第一步：开门
        ros::Time last_valid_control_ = ros::Time::now();
        double wait_time_ = 30;
        while(1)
        {
            door_msg.data = 1;
            access_control_pub_.publish(door_msg);

            sleep(3);

            nav_msgs::GetPlan get_plan_srv;
            get_plan_srv.request.start = start_pose;
            get_plan_srv.request.goal = end_pose;

            if(make_plan_client_.call(get_plan_srv))
            {
                int plan_size = get_plan_srv.response.plan.poses.size();
                double path_dis =  0.025*plan_size;
                log.debug("path_dis: %.9f\n",path_dis);
                if(path_dis < 2*reference_distance && path_dis>0)
                {
                    log.debug("门已打开，路径可通行");
                    res.ret = 0;
                    return true;
                }
                else
                {
                	log.debug("门处于关闭状态，等待");
                }
            }else{
                log.debug("规划路径失败，再次尝试");
               // res.ret = -3;
              //  return true;
            }

            if(ros::Time::now() > last_valid_control_ + ros::Duration(wait_time_))
            {
                door_msg.data = 0;
                access_control_pub_.publish(door_msg);
                log.debug("开门超时，失败");
                res.ret = -1;
                return true;
            }
        }
     }else if(req.cmd==2)
    { 
    	std_msgs::UInt8 door_msg;
    	door_msg.data = 0;
      access_control_pub_.publish(door_msg);
      log.debug("关门");
    }
      
    ros::WallDuration t_diff = ros::WallTime::now() - start;
    log.debug("original_path_srv cost time: %.9f\n", t_diff.toSec());
    
    return true;
 }
 
 

// 执行结束回调函数
void DoorThrough::doneCb(const actionlib::SimpleClientGoalState& state,const move_base_msgs::MoveBaseResultConstPtr &result)
{
    log.info("Navigation Finished");
	// 到达目标点
	if(state==actionlib::SimpleClientGoalState::SUCCEEDED)
	{
		log.debug("导航到目标点成功！");
        std_msgs::Bool motor_msg;
        motor_msg.data = true;
        motor_control_pub_.publish(motor_msg);
        door_through_state = 1;
        
        std_msgs::UInt8 door_msg;
        door_msg.data = 0;
        access_control_pub_.publish(door_msg);
        
    }else
    {
        log.debug("导航到目标点失败！");
        std_msgs::Bool motor_msg;
        motor_msg.data = false;
        motor_control_pub_.publish(motor_msg);
        door_through_state = 0;
    }

}

// 当目标激活的时候，会调用一次
void DoorThrough::ActiveCb() {
	log.info("激活目标");
	
}

// 接收服务器的反馈信息
void DoorThrough::FeedbackCb(const move_base_msgs::MoveBaseFeedbackConstPtr &	feedback)
{
	//ROS_INFO("Got Feedback of length %lu", feedback->sequence.size());
}


