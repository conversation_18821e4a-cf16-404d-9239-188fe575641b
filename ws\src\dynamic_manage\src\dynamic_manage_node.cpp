/**********************************************************************************
/**********************************************************************************
 *  文件名：       dynamic_manage_node.cpp
 *  版权：         南京驭行科技有限公司
 *  描述：         动态调参 
 *  修改人：       李冬琦 
 *  修改时间：     2019-12-31
 *  修改内容：     新增
 
 *  修改人：       何世政
 *  修改时间：     2020-10-16 15:30
 *  修改内容：     设置最大运动速度等级
 **********************************************************************************/
#include <ros/ros.h>
#include <ros/package.h>
#include <dynamic_manage/dynamic_manage.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float32.h>
#include <std_msgs/UInt8.h>

dwa_local_planner::DWAPlannerConfig g_config;

int acc_flag =0;

double set_speed_ = 0;

int data = 0;

void dynCallBack(const dwa_local_planner::DWAPlannerConfig &config)
{
    g_config = config;
}

void speed_level_call_back(const std_msgs::Float32 &msg)
{
	set_speed_ = msg.data;
}

void location_accuracy_call_back(const std_msgs::UInt8 &msg)
{
	data = msg.data;

}


int main(int argc, char *argv[])
{

	ros::init(argc, argv, "dynamic_manage_node");
	ros::NodeHandle nh;
	DynamicManage dynamicManage;
	log4cpp::Category& mlog = log4cpp::Category::getRoot(); 
	
	ros::Publisher robot_voice_pub_= nh.advertise<std_msgs::Bool>("robot_voice_speed",1);
	
	dynamic_reconfigure::Client<dwa_local_planner::DWAPlannerConfig> client("/move_base/DWAPlannerROS", dynCallBack);
	
	ros::Subscriber location_accuracy_ = nh.subscribe("/location_accuracy", 5, location_accuracy_call_back);  
	
	ros::Subscriber speed_level_ = nh.subscribe("/speed_level", 5, speed_level_call_back);  
	
	sleep(3);
	
	std_msgs::Bool voice_msg;
	
	bool speed_area = false;
	ros::Rate loop_rate(5);
	
	int set_speed_level_ =3;
	
	
	
	double system_max_rot_vel = 1.2;
	double system_max_vel_x   = 0.6;
	
	mlog.info("system_max_vel_x %f", system_max_vel_x );
	
	while(ros::ok())
    {
	    ros::spinOnce();
		
		double speed_line,speed_angular;
		bool speed_change;
		
		bool speed_charge_flag;
		int speed_level;
		dynamicManage.update_speed_level(speed_charge_flag,  speed_level);
		bool rotate_charge_flag;
		int rotate_level;
		dynamicManage.update_rotate_level(rotate_charge_flag,  rotate_level);
		dynamicManage.speed_change(speed_change,speed_line,speed_angular);
        //std::cout << "speed_change："<< speed_change<<std::endl;
		
		if(set_speed_ != 0)
		{
			system_max_vel_x = set_speed_;
			g_config.max_vel_x = set_speed_;
			client.setConfiguration(g_config);
			
			if(set_speed_<0.4)
			{
				set_speed_level_ = 1;
				mlog.info("当前速度档位为：低速");
				
				/*system("rosparam set /move_base/global_costmap/obstacle_layer/ls_scan/observation_persistence 1.5 &");
				system("rosparam set /move_base/global_costmap/obstacle_layer/scan/observation_persistence  1.5 &");
				system("rosparam set  /move_base/global_costmap/obstacle_layer/camera_depth/observation_persistence  1.5 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/ls_scan/observation_persistence  1.5 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/scan/observation_persistence  1.5 &");
				system("rosparam set  /move_base/local_costmap/obstacle_layer/camera_depth/observation_persistence  1.5 &");*/
				
				
			}else if(set_speed_<0.6 && set_speed_>=0.4)
			{
				mlog.info("当前速度档位为：中速");
				set_speed_level_ = 2;
				
				/*system("rosparam set /move_base/global_costmap/obstacle_layer/ls_scan/observation_persistence 1 &");
				system("rosparam set /move_base/global_costmap/obstacle_layer/scan/observation_persistence 1 &");
				system("rosparam set  /move_base/global_costmap/obstacle_layer/camera_depth/observation_persistence 1 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/ls_scan/observation_persistence 1 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/scan/observation_persistence 1 &");
				system("rosparam set  /move_base/local_costmap/obstacle_layer/camera_depth/observation_persistence 1 &");*/
				
			}
			else
			{
				mlog.info("当前速度档位为：高速");
				set_speed_level_ = 3;
				
			/*	system("rosparam set /move_base/global_costmap/obstacle_layer/ls_scan/observation_persistence 0.5 &");
				system("rosparam set /move_base/global_costmap/obstacle_layer/scan/observation_persistence 0.5 &");
				system("rosparam set  /move_base/global_costmap/obstacle_layer/camera_depth/observation_persistence 0.5 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/ls_scan/observation_persistence 0.5 &");
				system("rosparam set /move_base/local_costmap/obstacle_layer/scan/observation_persistence 0.5 &");
				system("rosparam set  /move_base/local_costmap/obstacle_layer/camera_depth/observation_persistence 0.5 &");*/
				
			}
			
		/*	if(set_speed_level_ == 3)
			{
				system("rosparam set /move_base/planner_frequency 10.0 &");
				system("rosparam set /move_base/controller_frequency 5.0 &");
			}
			else
			{
				system("rosparam set /move_base/planner_frequency 1.0 &");
				system("rosparam set /move_base/controller_frequency 5.0 &");	
						
			}
		*/
			set_speed_ = 0;		
		}
		
		if(set_speed_level_ == 3)
		{
			if(speed_change == true)
			{
				mlog.info("进入速度切换程序");
				mlog.info("g_config.max_rot_vel:%f , speed_angular:%f , g_config.max_vel_x: %f, speed_line: %f",g_config.max_rot_vel,speed_angular,g_config.max_vel_x,speed_line);
				if(g_config.max_rot_vel != speed_angular&& g_config.max_vel_x!=speed_line)
				{
					g_config.max_rot_vel = speed_angular;
					g_config.max_vel_x = speed_line;
					client.setConfiguration(g_config);
					std::cout<<"进入减速区------------------------------------"<<std::endl;
					speed_area=true;
					voice_msg.data=true;
					robot_voice_pub_.publish(voice_msg);	
				}	
				loop_rate.sleep();
				continue;
			}
			else if(speed_area)
			{
					g_config.max_rot_vel = system_max_rot_vel;
					g_config.max_vel_x = system_max_vel_x;
					client.setConfiguration(g_config);
					speed_area = false;
					voice_msg.data=false;
					std::cout<<"恢复正常速度+++++++++++++++++++++++++++++++++"<<std::endl;	
					robot_voice_pub_.publish(voice_msg);				
			}

			if(speed_charge_flag == true && speed_area == false)
			{
				switch(speed_level){
					case LOW_1:
						g_config.max_vel_x = 0.3*system_max_vel_x;
						//g_config.acc_lim_x = 0.1;
						mlog.info("转换为低速模LOW_1 %f", 0.3*system_max_vel_x );
						client.setConfiguration(g_config);
						break;
					case LOW_2:
						g_config.max_vel_x = 0.5*system_max_vel_x;
						//g_config.acc_lim_x = 0.2;
						mlog.info("转换为低速模LOW_2 %f", 0.5*system_max_vel_x);
						client.setConfiguration(g_config);
						break;
					case LOW_3:
						g_config.max_vel_x = 0.7*system_max_vel_x;
						//g_config.acc_lim_x = 0.3;
						mlog.info("转换为低速模LOW_3 %f", 0.7*system_max_vel_x);
						client.setConfiguration(g_config);
						break;
					case LOW_4:
						g_config.max_vel_x = 0.9*system_max_vel_x;
						//g_config.acc_lim_x = 0.4;
						mlog.info("转换为低速模LOW_4 %f", 0.9*system_max_vel_x);
						client.setConfiguration(g_config);
						break;
					case LOW_5:
						g_config.max_vel_x = system_max_vel_x;
						//g_config.acc_lim_x = 0.5;
						mlog.info("转换为低速模LOW_5 %f",system_max_vel_x);
						client.setConfiguration(g_config);
						break;
					case NORMAL:
						g_config.max_vel_x = system_max_vel_x;
						//g_config.acc_lim_x = 0.6;
						mlog.info("转换为普速模NORMAL %f", system_max_vel_x);
						client.setConfiguration(g_config);
						break;	
					default:					
						break;
				}
			}

			if(rotate_charge_flag == true)
			{
				switch(rotate_level){
					case RLOW:
						//g_config.min_rot_vel = 0.6;
						//g_config.acc_lim_theta = 1.2;
						//mlog.info("----------------------------------转换为低转速模式");
						//std::cout<<"转换为低速模式"<<std::endl;
						//client.setConfiguration(g_config);
						break;
					case RNORMAL:					
						break;
					case RFAST:
						//g_config.min_rot_vel = 0.3;
						//g_config.acc_lim_theta = 2.4;
						//mlog.info("----------------------------------转换为高转速模式");
						//std::cout<<"转换为普速模式"<<std::endl;
						//client.setConfiguration(g_config);
						break;			
					default:					
						break;
				}
			}

		}
		
				
		if(data == 1)
		{
			g_config.xy_goal_tolerance = 0.2;
			g_config.yaw_goal_tolerance = 0.1;

			client.setConfiguration(g_config);
			mlog.info("普通精度模式");
			data = 0;
 		}

		if(data == 2)
		{
			mlog.info("高精度模式");
			g_config.xy_goal_tolerance = 0.08;
			g_config.yaw_goal_tolerance = 0.08;

			client.setConfiguration(g_config);
			
			data = 0;
		}
		if(data == 3)
		{
			mlog.info("方位零精度模式");
			g_config.yaw_goal_tolerance = 10;
			g_config.xy_goal_tolerance = 0.1;
			client.setConfiguration(g_config);
			
			data = 0;
		}
		if(data == 4)
		{
			mlog.info("超低精度模式");
			g_config.xy_goal_tolerance = 0.4;
			g_config.yaw_goal_tolerance = 0.5;

			client.setConfiguration(g_config);
			data = 0;
		}
	

		
		loop_rate.sleep();
	}
	
		
	ros::spin();
	return 0;
}




